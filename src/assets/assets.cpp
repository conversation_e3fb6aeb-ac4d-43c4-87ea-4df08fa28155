#include "assets.hpp"

// C++ standard library
#include <exception>
#include <iostream>
#include <memory>
#include <stdexcept>
#include <string>
#include <utility>

// Local includes
#include "asset_data_types.hpp"
#include "loaders/audio_loader.hpp"
#include "loaders/font_loader.hpp"
#include "loaders/image_loader.hpp"
#include "loaders/mesh_loader.hpp"
#include "loaders/model_loader.hpp"
#include "loaders/shader_loader.hpp"

namespace IronFrost {
  AssetsManager::AssetsManager(IVFS &vfs) : m_vfs(vfs) {}

  std::unique_ptr<ShaderData> AssetsManager::loadShader(const std::string& path) {
    ShaderLoader shaderLoader(m_vfs);
    return shaderLoader.loadShader(path);
  }

  const ShaderData& AssetsManager::loadShader(const StringID& name, const std::string& path) {
    std::unique_ptr<ShaderData> shaderData = loadShader(path);
    m_shaders.try_emplace(name, std::move(shaderData));

    return *m_shaders.at(name);
  }

  void AssetsManager::unloadShader(const StringID& name) {
    auto it = m_shaders.find(name);
    if (it != m_shaders.end()) {
      m_shaders.erase(it);
    }
  }

  const ShaderData& AssetsManager::getShader(const StringID& name) const {
    auto it = m_shaders.find(name);
    if (it != m_shaders.end()) {
      return *it->second;
    }

    throw std::runtime_error("Shader not found");
  }
  
  std::unique_ptr<MeshData> AssetsManager::createPrimitive(PrimitiveType type, const PrimitiveParams& params) {
    MeshLoader meshLoader(m_vfs);
    return meshLoader.createPrimitive(type, params);
  }

  const MeshData& AssetsManager::createPrimitive(const StringID& name, PrimitiveType type, const PrimitiveParams& params) {
    std::unique_ptr<MeshData> meshData = createPrimitive(type, params);
    m_meshes.try_emplace(name, std::move(meshData));

    return *m_meshes.at(name);
  }

  void AssetsManager::unloadMesh(const StringID& name) {
    auto it = m_meshes.find(name);
    if (it != m_meshes.end()) {
      m_meshes.erase(it);
    }
  }

  const MeshData& AssetsManager::getMesh(const StringID& name) const {
    auto it = m_meshes.find(name);
    if (it != m_meshes.end()) {
      return *it->second;
    }

    throw std::runtime_error("Mesh not found");
  }

  std::unique_ptr<ModelData> AssetsManager::loadModel(const std::string& path) {
    ModelLoader modelLoader(m_vfs);
    return modelLoader.loadModel(path);
  }

  const ModelData& AssetsManager::loadModel(const StringID& name, const std::string& path) {
    std::unique_ptr<ModelData> modelData = loadModel(path);
    m_models.try_emplace(name, std::move(modelData));

    return *m_models.at(name);
  }

  void AssetsManager::unloadModel(const StringID& name) {
    auto it = m_models.find(name);
    if (it != m_models.end()) {
      m_models.erase(it);
    }
  }

  const ModelData& AssetsManager::getModel(const StringID& name) const {
    auto it = m_models.find(name);
    if (it != m_models.end()) {
      return *it->second;
    }

    throw std::runtime_error("Model not found");
  }

  std::unique_ptr<ImageData> AssetsManager::loadImage(const std::string& path) {
    ImageLoader imageLoader(m_vfs);
    return imageLoader.loadImage(path);
  }

  const ImageData& AssetsManager::loadImage(const StringID& name, const std::string& path) {
    std::unique_ptr<ImageData> imageData = loadImage(path);
    m_images.try_emplace(name, std::move(imageData));

    return *m_images.at(name);
  }

  void AssetsManager::unloadImage(const StringID& name) {
    auto it = m_images.find(name);
    if (it != m_images.end()) {
      m_images.erase(it);
    }
  }

  const ImageData& AssetsManager::getImage(const StringID& name) const {
    auto it = m_images.find(name);
    if (it != m_images.end()) {
      return *it->second;
    }

    throw std::runtime_error("Texture not found");
  }

  std::unique_ptr<AudioData> AssetsManager::loadAudio(const std::string& path) {
    AudioLoader audioLoader(m_vfs);
    return audioLoader.loadAudioFile(path);
  }

  const AudioData& AssetsManager::loadAudio(const StringID& name, const std::string& path) {
    std::unique_ptr<AudioData> audioData = loadAudio(path);
    m_audio.try_emplace(name, std::move(audioData));

    return *m_audio.at(name);
  }

  void AssetsManager::unloadAudio(const StringID& name) {
    auto it = m_audio.find(name);
    if (it != m_audio.end()) {
      m_audio.erase(it);
    }
  }

  const AudioData& AssetsManager::getAudio(const StringID& name) const {
    auto it = m_audio.find(name);
    if (it != m_audio.end()) {
      return *it->second;
    }

    throw std::runtime_error("Audio not found");
  }

  std::unique_ptr<FontData> AssetsManager::loadFont(const std::string& path, unsigned int size) {
    FontLoader fontLoader(m_vfs);
    return fontLoader.loadFont(path, size);
  }

  const FontData& AssetsManager::loadFont(const StringID& name, const std::string& path, unsigned int size) {
    std::unique_ptr<FontData> fontData = loadFont(path, size);
    m_fonts.try_emplace(name, std::move(fontData));

    return *m_fonts.at(name);
  }

  void AssetsManager::unloadFont(const StringID& name) {
    auto it = m_fonts.find(name);
    if (it != m_fonts.end()) {
      m_fonts.erase(it);
    }
  }

  const FontData& AssetsManager::getFont(const StringID& name) const {
    auto it = m_fonts.find(name);
    if (it != m_fonts.end()) {
      return *it->second;
    }

    throw std::runtime_error("Font not found");
  }
}
