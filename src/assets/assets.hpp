#ifndef __IF__ASSETS_HPP
#define __IF__ASSETS_HPP

// C++ standard library
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../utils/string_id.hpp"
#include "asset_data_types.hpp"
#include "asset_primitive_types.hpp"

namespace IronFrost {
  class IVFS;
  
  class AssetsManager {
    private:
      IVFS& m_vfs;

      std::unordered_map<StringID, std::unique_ptr<ShaderData>> m_shaders;
      std::unordered_map<StringID, std::unique_ptr<MeshData>> m_meshes;
      std::unordered_map<StringID, std::unique_ptr<ModelData>> m_models;
      std::unordered_map<StringID, std::unique_ptr<ImageData>> m_images;
      std::unordered_map<StringID, std::unique_ptr<AudioData>> m_audio;
      std::unordered_map<StringID, std::unique_ptr<FontData>> m_fonts;

    public:
      explicit AssetsManager(IVFS& vfs);
      AssetsManager(const AssetsManager &) = delete;

      std::unique_ptr<ShaderData> loadShader(const std::string& path);
      const ShaderData& loadShader(const StringID& name, const std::string& path);
      void unloadShader(const StringID& name);
      const ShaderData& getShader(const StringID& name) const;

      std::unique_ptr<MeshData> createPrimitive(PrimitiveType type, const PrimitiveParams& params = {});
      const MeshData& createPrimitive(const StringID& name, PrimitiveType type, const PrimitiveParams& params = {});
      void unloadMesh(const StringID& name);
      const MeshData& getMesh(const StringID& name) const;

      std::unique_ptr<ModelData> loadModel(const std::string& path);
      const ModelData& loadModel(const StringID& name, const std::string& path);
      void unloadModel(const StringID& name);
      const ModelData& getModel(const StringID& name) const;

      std::unique_ptr<ImageData> loadImage(const std::string& path);
      const ImageData& loadImage(const StringID& name, const std::string& path);
      void unloadImage(const StringID& name);
      const ImageData& getImage(const StringID& name) const;

      std::unique_ptr<AudioData> loadAudio(const std::string& path);
      const AudioData& loadAudio(const StringID& name, const std::string& path);
      void unloadAudio(const StringID& name);
      const AudioData& getAudio(const StringID& name) const;

      std::unique_ptr<FontData> loadFont(const std::string& path, unsigned int size);
      const FontData& loadFont(const StringID& name, const std::string& path, unsigned int size);
      void unloadFont(const StringID& name);
      const FontData& getFont(const StringID& name) const;
    };
}

#endif
