#ifndef __IF__ASSET_DATA_TYPES_HPP
#define __IF__ASSET_DATA_TYPES_HPP

// C++ standard library
#include <array>
#include <memory>
#include <optional>
#include <string>
#include <variant>
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../utils/string_id.hpp"
#include "../utils/collision_math.hpp"

namespace IronFrost {
  // pos.x pos.y pos.z uv.x uv.y norm.x norm.y norm.z tan.x tan.y tan.z btan.x btan.y btan.z
  struct Vertex {
    std::array<float, 14> data;

    glm::vec3 getPosition() const {
      return glm::vec3(data[0], data[1], data[2]);
    }

    glm::vec2 getUV() const {
      return glm::vec2(data[3], data[4]);
    }

    glm::vec3 getNormal() const {
      return glm::vec3(data[5], data[6], data[7]);
    }

    void setTangent(const glm::vec3& tangent) {
      data[8] = tangent.x;
      data[9] = tangent.y;
      data[10] = tangent.z;
    }

    glm::vec3 getTangent() const {
      return glm::vec3(data[8], data[9], data[10]);
    }

    void setBitangent(const glm::vec3& bitangent) {
      data[11] = bitangent.x;
      data[12] = bitangent.y;
      data[13] = bitangent.z;
    }

    glm::vec3 getBitangent() const {
      return glm::vec3(data[11], data[12], data[13]);
    }
  };

  struct ShaderData {
    std::string vertexShader;
    std::string fragmentShader;
  };

  struct MeshData {
    std::vector<Vertex> vertices{};
    std::vector<unsigned int> indices{};

    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};

    void calculateBounds() {
      glm::vec3 minPos = vertices[0].getPosition();
      glm::vec3 maxPos = vertices[0].getPosition();

      for (const auto& vertex : vertices) {
        glm::vec3 pos = vertex.getPosition();
        minPos = glm::min(minPos, pos);
        maxPos = glm::max(maxPos, pos);
      }

      bounds = CollisionMath::AABB{minPos, maxPos};
    }

    void calculateTangentsAndBitangents() {
      for (size_t i = 0; i < indices.size(); i += 3) {
        unsigned int i0 = indices[i]; 
        unsigned int i1 = indices[i + 1];
        unsigned int i2 = indices[i + 2];

        const glm::vec3& v0 = vertices[i0].getPosition();
        const glm::vec3& v1 = vertices[i1].getPosition();
        const glm::vec3& v2 = vertices[i2].getPosition();

        const glm::vec2& uv0 = vertices[i0].getUV();
        const glm::vec2& uv1 = vertices[i1].getUV();
        const glm::vec2& uv2 = vertices[i2].getUV();

        glm::vec3 edge1 = v1 - v0;
        glm::vec3 edge2 = v2 - v0;

        glm::vec2 deltaUV1 = uv1 - uv0;
        glm::vec2 deltaUV2 = uv2 - uv0;

        float f = 1.0f / (deltaUV1.x * deltaUV2.y - deltaUV1.y * deltaUV2.x);

        glm::vec3 tangent;
        tangent.x = f * (deltaUV2.y * edge1.x - deltaUV1.y * edge2.x);
        tangent.y = f * (deltaUV2.y * edge1.y - deltaUV1.y * edge2.y);
        tangent.z = f * (deltaUV2.y * edge1.z - deltaUV1.y * edge2.z);
        tangent = glm::normalize(tangent);

        glm::vec3 bitangent;
        bitangent.x = f * (-deltaUV2.x * edge1.x + deltaUV1.x * edge2.x);
        bitangent.y = f * (-deltaUV2.x * edge1.y + deltaUV1.x * edge2.y);
        bitangent.z = f * (-deltaUV2.x * edge1.z + deltaUV1.x * edge2.z);
        bitangent = glm::normalize(bitangent);

        vertices[i0].setTangent(vertices[i0].getTangent() + tangent);
        vertices[i1].setTangent(vertices[i1].getTangent() + tangent);
        vertices[i2].setTangent(vertices[i2].getTangent() + tangent);

        vertices[i0].setBitangent(vertices[i0].getBitangent() + bitangent);
        vertices[i1].setBitangent(vertices[i1].getBitangent() + bitangent);
        vertices[i2].setBitangent(vertices[i2].getBitangent() + bitangent);
      }

      for (auto& vertex : vertices) {
        vertex.setTangent(glm::normalize(vertex.getTangent()));
        vertex.setBitangent(glm::normalize(vertex.getBitangent()));
      }
    }
  };

  struct ImageData {
    int width{0};
    int height{0};
    int channels{0};

    std::shared_ptr<void> data;

    bool is16bit{false};

    ImageData();

    unsigned char* get8BitData() const {
      return static_cast<unsigned char*>(data.get());
    }
    unsigned short* get16BitData() const {
      return static_cast<unsigned short*>(data.get());
    }
  };

  struct ImageAtlasData {
    struct Entry {
      glm::vec2 position;
      glm::vec2 uvMin;
      glm::vec2 uvMax;

      int width{0};
      int height{0};
    };

    std::unique_ptr<ImageData> imageData;
    std::vector<Entry> entries;
  };

  struct ModelDataNode {
    glm::mat4 transform{1.0f};

    std::vector<std::unique_ptr<MeshData>> meshes;
    std::vector<std::unique_ptr<ImageData>> textures;
    std::vector<ModelDataNode> children;
  };

  struct ModelData {
    ModelDataNode rootNode;

    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};
  };

  struct GlyphData {
    glm::vec2 size;
    glm::vec2 bearing;
    unsigned int advance;

    std::vector<unsigned char> bitmap;
  };

  struct FontData {
    std::vector<GlyphData> glyphs;
    unsigned int size;
    unsigned int lineHeight;
  };

  struct AudioData {
    std::vector<float> samples;  // Normalized float samples (-1.0 to 1.0)
    int sampleRate;              // Sample rate (e.g., 44100 Hz)
    int channels;                // Number of channels (1 = mono, 2 = stereo)
    float duration;              // Duration in seconds

    size_t getFrameCount() const {
      return samples.size() / channels;
    }

    std::vector<int16_t> getInt16Samples() const {
      std::vector<int16_t> int16Samples;
      int16Samples.reserve(samples.size());

      for (float sample : samples) {
        float clamped = std::max(-1.0f, std::min(1.0f, sample));
        int16Samples.push_back(static_cast<int16_t>(clamped * 32767.0f));
      }

      return int16Samples;
    }
  };

  struct BlinnPhongMaterialData {
    glm::vec3 ambient;
    glm::vec3 diffuse;
    glm::vec3 specular;
    float shininess;

    StringID textureName;
  };

  struct PBRMaterialData {
    std::vector<std::unique_ptr<ImageData>> textureArray{};
  };

  struct MaterialData {
    std::variant<BlinnPhongMaterialData, PBRMaterialData> data{};
  };
}

#endif
