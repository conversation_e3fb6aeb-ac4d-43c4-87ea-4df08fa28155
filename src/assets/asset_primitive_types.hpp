#ifndef __IF__ASSET_PRIMITIVE_TYPES_HPP
#define __IF__ASSET_PRIMITIVE_TYPES_HPP

// C++ standard library
#include <exception>
#include <optional>
#include <string>
#include <unordered_map>
#include <variant>

// Local includes
#include "../utils/json_serializers.hpp"

namespace IronFrost {
  enum PrimitiveType {
    TRIANGLE,
    QUAD,
    CUBE,
    SPHERE,
    PLANE
  };

  using json = nlohmann::json;
  using ParamValue = std::variant<int, unsigned int, float, double, bool, std::string, glm::vec2, glm::vec3, glm::vec4>;

  struct PrimitiveParams {
    std::unordered_map<std::string, ParamValue> values;

    template<typename T>
    std::optional<T> get(const std::string& key) const {
      auto it = values.find(key);
      if (it != values.end()) {
        if (auto val = std::get_if<T>(&it->second)) {
          return *val;
        }
      }
      return std::nullopt;
    }

    template<typename T>
    void set(const std::string& key, const T& value) {
      values[key] = value;
    }

    static PrimitiveParams fromJSON(const json& j) {
      PrimitiveParams params;

      for (auto& el : j.items()) {
        const std::string& key = el.key();
        const auto& value = el.value();

        if (value.is_number_integer()) {
          params.set(key, value.get<int>());
        } else if (value.is_number_float()) {
          params.set(key, value.get<float>());
        } else if (value.is_boolean()) {
          params.set(key, value.get<bool>());
        } else if (value.is_string()) {
          params.set(key, value.get<std::string>());
        } else if (value.is_array()) {
          if (value.size() == 2) {
            params.set(key, glm::vec2(value[0].get<float>(), value[1].get<float>()));
          } else if (value.size() == 3) {
            params.set(key, glm::vec3(value[0].get<float>(), value[1].get<float>(), value[2].get<float>()));
          } else if (value.size() == 4) {
            params.set(key, glm::vec4(value[0].get<float>(), value[1].get<float>(), value[2].get<float>(), value[3].get<float>()));
          }
        } else {
          throw std::runtime_error("Unknown primitive param type");
        }
      }

      return params;
    }
  };
}

#endif
