#include "assets_loader.hpp"

// C++ standard library
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

// Local includes
#include "../utils/string_id.hpp"
#include "../vfs/vfs.hpp"
#include "assets.hpp"
#include "loaders/image_loader.hpp"
#include "processors/image_atlas_processor.hpp"
#include "writers/image_writer.hpp"
#include "generators/default_texture_generator.hpp"

namespace IronFrost {

  const std::unordered_map<std::string, PrimitiveType> AssetsLoader::primitiveTypes = {
    {"triangle", PrimitiveType::TRIANGLE},
    {"quad", PrimitiveType::QUAD},
    {"cube", PrimitiveType::CUBE},
    {"sphere", PrimitiveType::SPHERE},
    {"plane", PrimitiveType::PLANE}
  };
  
  std::unique_ptr<AssetsLoader> AssetsLoader::fromFile(IVFS& vfs, const std::string& path) {
    return std::make_unique<AssetsFromFileLoader>(vfs, path);
  }

  BlinnPhongMaterialData AssetsFromFileLoader::loadBlinnPhongMaterial(AssetsManager& assetsManager, const json& material) const {
    BlinnPhongMaterialData blinnPhongMaterialData;

    blinnPhongMaterialData.ambient = material.contains("ambient") 
      ? glm::vec3{ material["ambient"][0], material["ambient"][1], material["ambient"][2] } 
      : glm::vec3(1.0F);

    blinnPhongMaterialData.diffuse = material.contains("diffuse")
      ? glm::vec3{ material["diffuse"][0], material["diffuse"][1], material["diffuse"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.specular = material.contains("specular")
      ? glm::vec3{ material["specular"][0], material["specular"][1], material["specular"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.shininess = material.contains("shininess")
      ? material["shininess"].get<float>()
      : 32.0F;

    blinnPhongMaterialData.textureName = StringID(material.contains("texture") ? material["texture"] : "");

    return blinnPhongMaterialData;
  }

  PBRMaterialData AssetsFromFileLoader::loadPBRMaterial(AssetsManager& assetsManager, const json& material) const {
    // BaseColor is required for PBR materials
    if (!material.contains("baseColor")) {
      throw std::runtime_error("PBR material must have a baseColor texture");
    }

    PBRMaterialData pbrMaterialData;
    pbrMaterialData.textureArray.reserve(6);

    // Load baseColor texture to determine reference size
    auto baseColorTexture = assetsManager.loadImage(material["baseColor"]);
    int width = baseColorTexture->width;
    int height = baseColorTexture->height;

    // Define texture types and their default generators
    struct TextureInfo {
      const char* key;
      std::function<std::unique_ptr<ImageData>(int, int)> defaultGenerator;
    };

    std::vector<TextureInfo> textureTypes = {
      {"baseColor", [](int w, int h) { return DefaultTextureGenerator::createBaseColorTexture(w, h); }},
      {"normal", [](int w, int h) { return DefaultTextureGenerator::createNormalTexture(w, h); }},
      {"metallic", [](int w, int h) { return DefaultTextureGenerator::createMetallicTexture(w, h); }},
      {"roughness", [](int w, int h) { return DefaultTextureGenerator::createRoughnessTexture(w, h); }},
      {"ao", [](int w, int h) { return DefaultTextureGenerator::createAOTexture(w, h); }},
      {"emissive", [](int w, int h) { return DefaultTextureGenerator::createEmissiveTexture(w, h); }}
    };

    // Add baseColor texture first
    pbrMaterialData.textureArray.emplace_back(std::move(baseColorTexture));

    // Load or generate remaining textures
    for (size_t i = 1; i < textureTypes.size(); i++) {
      const auto& textureType = textureTypes[i];

      if (material.contains(textureType.key)) {
        pbrMaterialData.textureArray.emplace_back(assetsManager.loadImage(material[textureType.key]));
      } else {
        pbrMaterialData.textureArray.emplace_back(textureType.defaultGenerator(width, height));
      }
    }

    return pbrMaterialData;
  }

  AssetsFromFileLoader::AssetsFromFileLoader(IVFS &vfs, const std::string &path) :
    m_vfs(vfs)
  {
    m_assetsConfig = json::parse(m_vfs.readFile(path));
  }

  void AssetsFromFileLoader::loadMeshes(AssetsManager& assetsManager, std::function<void(const StringID name, const MeshData&)> callback) const {
    if(!m_assetsConfig.contains("meshes")) {
      return;
    }

    auto meshesConfig = m_assetsConfig["meshes"];

    for (json::const_iterator it = meshesConfig.begin(); it != meshesConfig.end(); ++it) {
      auto mesh = it.value();

      StringID name = StringID(mesh["name"]);
      std::string type = mesh["type"];

      if(type == "primitive") {
        std::string primitive = mesh["primitive"];
        auto params = PrimitiveParams::fromJSON(mesh);
        callback(name, assetsManager.createPrimitive(name, primitiveTypes.at(primitive), params));
      }
    }
  }

  void AssetsFromFileLoader::loadModels(AssetsManager& assetsManager, std::function<void(const StringID name,const ModelData&)> callback) const {
    if(!m_assetsConfig.contains("models")) {
      return;
    }

    auto modelsConfig = m_assetsConfig["models"];

    for (json::const_iterator it = modelsConfig.begin(); it != modelsConfig.end(); ++it) {
      auto model = it.value();

      StringID name = StringID(model["name"]);
      std::string path = model["path"];

      callback(name, assetsManager.loadModel(name, path));
    }
  }

  void AssetsFromFileLoader::loadShaders(AssetsManager& assetsManager, std::function<void(const StringID name, const ShaderData&)> callback) const {
    if(!m_assetsConfig.contains("shaders")) {
      return;
    }

    auto shadersConfig = m_assetsConfig["shaders"];

    for (json::const_iterator it = shadersConfig.begin(); it != shadersConfig.end(); ++it) {
      auto shader = it.value();

      StringID name = StringID(shader["name"]);
      std::string path = shader["path"];

      callback(name, assetsManager.loadShader(name, path));
    }
  }

  void AssetsFromFileLoader::loadTextures(AssetsManager& assetsManager, std::function<void(const StringID name, const ImageData&)> callback) const {
    if(!m_assetsConfig.contains("textures")) {
      return;
    }

    auto texturesConfig = m_assetsConfig["textures"];

    for (json::const_iterator it = texturesConfig.begin(); it != texturesConfig.end(); ++it) {
      auto texture = it.value();
      
      StringID name = StringID(texture["name"]);
      std::string path = texture["path"];

      callback(name, assetsManager.loadImage(name, path));
    }
  }

  void AssetsFromFileLoader::loadAudio(AssetsManager& assetsManager, std::function<void(const StringID name, const AudioData&)> callback) const {
    if(!m_assetsConfig.contains("audio")) {
      return;
    }

    auto audioConfig = m_assetsConfig["audio"];

    for (json::const_iterator it = audioConfig.begin(); it != audioConfig.end(); ++it) {
      auto audio = it.value();

      StringID name = StringID(audio["name"]);
      std::string path = audio["path"];

      callback(name, assetsManager.loadAudio(name, path));
    }
  }

  void AssetsFromFileLoader::loadFonts(AssetsManager& assetsManager, std::function<void(const StringID name, const FontData&)> callback) const {
    if(!m_assetsConfig.contains("fonts")) {
      return;
    }

    auto fontsConfig = m_assetsConfig["fonts"];

    for (json::const_iterator it = fontsConfig.begin(); it != fontsConfig.end(); ++it) {
      auto font = it.value();
      
      StringID name = StringID(font["name"]);
      std::string path = font["path"];
      int size = font["size"];

      callback(name, assetsManager.loadFont(name, path, size));
    }
  }

  void AssetsFromFileLoader::loadMaterials(AssetsManager& assetsManager, std::function<void(const StringID, const MaterialData&)> callback) const {
    if(!m_assetsConfig.contains("materials")) {
      return;
    }

    std::cout << "loading materials" << '\n';

    auto materialsConfig = m_assetsConfig["materials"];

    for (json::const_iterator it = materialsConfig.begin(); it != materialsConfig.end(); ++it) {
      auto material = it.value();

      StringID name = StringID(material["name"]);
      MaterialData materialData;

      if (material["type"] == "phong") {
        materialData.data = loadBlinnPhongMaterial(assetsManager, material);
      } else if (material["type"] == "pbr") {
        materialData.data = loadPBRMaterial(assetsManager, material);
      }
      
      callback(name, materialData);
    }
  }

  void AssetsFromFileLoader::loadPostprocessEffects(std::function<void(const StringID name, const std::vector<std::string>& shaderNames)> callback) const {
    if(!m_assetsConfig.contains("postprocess")) {
      return;
    }

    auto postprocessConfig = m_assetsConfig["postprocess"];

    for (json::const_iterator it = postprocessConfig.begin(); it != postprocessConfig.end(); ++it) {
      auto postprocess = it.value();

      StringID name = StringID(postprocess["name"]);
      std::vector<std::string> shaderNames = postprocess["shaders"].get<std::vector<std::string>>();

      callback(name, shaderNames);
    }
  }


}
