#ifndef __IF__ASSETS_LOADER_HPP
#define __IF__ASSETS_LOADER_HPP

// C++ standard library
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

// Third-party libraries
#include <nlohmann/json.hpp>

// Local includes
#include "asset_data_types.hpp"
#include "asset_primitive_types.hpp"

using json = nlohmann::json;

namespace IronFrost {
  class IVFS;
  class AssetsManager;
  class AssetsFromFileLoader;
  class StringID;

  class AssetsLoader {
    public:
      AssetsLoader() = default;
      virtual ~AssetsLoader() = default;

      static std::unique_ptr<AssetsLoader> fromFile(IVFS& vfs, const std::string& path);

      static const std::unordered_map<std::string, PrimitiveType> primitiveTypes;

      virtual void loadMeshes(AssetsManager& assetsManager, std::function<void(const StringID, const MeshData&)> callback) const = 0;
      virtual void loadModels(AssetsManager& assetsManager, std::function<void(const StringID, const ModelData&)> callback) const = 0;
      virtual void loadShaders(AssetsManager& assetsManager, std::function<void(const StringID, const ShaderData&)> callback) const = 0;
      virtual void loadTextures(AssetsManager& assetsManager, std::function<void(const StringID, const ImageData&)> callback) const = 0;
      virtual void loadAudio(AssetsManager& assetsManager, std::function<void(const StringID, const AudioData&)> callback) const = 0;
      virtual void loadFonts(AssetsManager& assetsManager, std::function<void(const StringID, const FontData&)> callback) const = 0;
      virtual void loadMaterials(AssetsManager& assetsManager, std::function<void(const StringID, const MaterialData&)> callback) const = 0;
      virtual void loadPostprocessEffects(std::function<void(const StringID _name, const std::vector<std::string>& _shaderNames)> callback) const = 0;
  };

  class AssetsFromFileLoader : public AssetsLoader {
    private:
      IVFS &m_vfs;

      json m_assetsConfig;

      BlinnPhongMaterialData loadBlinnPhongMaterial(AssetsManager& assetsManager, const json& material) const;
      PBRMaterialData loadPBRMaterial(AssetsManager& assetsManager, const json& material) const;
    public:
      AssetsFromFileLoader(IVFS &vfs, const std::string &_path);

      void loadMeshes(AssetsManager& assetsManager, std::function<void(const StringID _name, const MeshData &)> callback) const override;
      void loadModels(AssetsManager& assetsManager, std::function<void(const StringID _name, const ModelData&)> callback) const override;
      void loadShaders(AssetsManager& assetsManager, std::function<void(const StringID _name, const ShaderData &)> callback) const override;
      void loadTextures(AssetsManager& assetsManager, std::function<void(const StringID _name, const ImageData &)> callback) const override;
      void loadAudio(AssetsManager& assetsManager, std::function<void(const StringID _name, const AudioData &)> callback) const override;
      void loadFonts(AssetsManager& assetsManager, std::function<void(const StringID _name, const FontData &)> callback) const override;
      void loadMaterials(AssetsManager& assetsManager, std::function<void(const StringID, const MaterialData&)> callback) const override;
      void loadPostprocessEffects(std::function<void(const StringID _name, const std::vector<std::string>& _shaderNames)> callback) const override;
  };
}

#endif
