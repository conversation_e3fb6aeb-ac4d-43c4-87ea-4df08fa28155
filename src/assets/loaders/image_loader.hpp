#ifndef __IF__IMAGE_LOADER_HPP
#define __IF__IMAGE_LOADER_HPP

// C++ standard library
#include <memory>
#include <string>
#include <vector>

namespace IronFrost {
  class IVFS;
  struct ImageData;
  struct ImageAtlasData;

  class ImageLoader {
    private:
      IVFS& m_vfs;

      std::vector<unsigned char> readImageFile(const std::string& path) const;
      bool validateImageFormat(const unsigned char* data, int dataSize, const std::string& path) const;
      bool loadImageData(ImageData* imageData, const unsigned char* data, int dataSize, const std::string& path) const;

    public:
      explicit ImageLoader(IVFS& vfs);

      std::unique_ptr<ImageData> loadImage(const std::string& path) const;
  };
}

#endif
