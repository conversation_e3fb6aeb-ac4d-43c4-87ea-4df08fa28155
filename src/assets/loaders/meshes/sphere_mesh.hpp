#ifndef __IF__SPHERE_MESH_HPP
#define __IF__SPHERE_MESH_HPP

// C++ standard library
#include <memory>
#include <utility>
#include <vector>

// Third-party libraries
#include <glm/gtc/constants.hpp>

// Local includes
#include "../../asset_data_types.hpp"

namespace IronFrost {
  class SphereMesh {
    public:
      static std::unique_ptr<MeshData> createMeshData(int latitudeBands, int longitudeBands) {
        std::vector<Vertex> vertices;
        std::vector<unsigned int> indices;

        generateVertices(vertices, latitudeBands, longitudeBands);
        generateIndices(indices, latitudeBands, longitudeBands);

        auto data = std::make_unique<MeshData>(MeshData{std::move(vertices), std::move(indices)});

        data->calculateTangentsAndBitangents();
        data->calculateBounds();

        return data;
      }

    private:
      static void generateVertices(std::vector<Vertex>& vertices, int latitudeBands, int longitudeBands) {
        // Reserve space for better performance
        const size_t vertexCount = (latitudeBands + 1) * (longitudeBands + 1);
        vertices.reserve(vertexCount);

        for (int lat = 0; lat <= latitudeBands; ++lat) {
          const float theta = lat * glm::pi<float>() / latitudeBands;
          const float sinTheta = glm::sin(theta);
          const float cosTheta = glm::cos(theta);

          for (int lon = 0; lon <= longitudeBands; ++lon) {
            const float phi = lon * 2.0f * glm::pi<float>() / longitudeBands;
            const float sinPhi = glm::sin(phi);
            const float cosPhi = glm::cos(phi);

            // Calculate position on unit sphere
            const float x = cosPhi * sinTheta;
            const float y = cosTheta;
            const float z = sinPhi * sinTheta;

            // Calculate UV coordinates
            const float u = 1.0f - (static_cast<float>(lon) / longitudeBands);
            const float v = 1.0f - (static_cast<float>(lat) / latitudeBands);

            // Scale to radius 0.5 and use normalized position as normal
            vertices.push_back(Vertex{0.5f * x, 0.5f * y, 0.5f * z, u, v, x, y, z});
          }
        }
      }

      static void generateIndices(std::vector<unsigned int>& indices, int latitudeBands, int longitudeBands) {
        // Reserve space for better performance
        const size_t indexCount = latitudeBands * longitudeBands * 6;
        indices.reserve(indexCount);

        for (int lat = 0; lat < latitudeBands; ++lat) {
          for (int lon = 0; lon < longitudeBands; ++lon) {
            const int first = (lat * (longitudeBands + 1)) + lon;
            const int second = first + longitudeBands + 1;

            // First triangle (counter-clockwise)
            indices.push_back(first);
            indices.push_back(first + 1);
            indices.push_back(second);

            // Second triangle (counter-clockwise)
            indices.push_back(first + 1);
            indices.push_back(second + 1);
            indices.push_back(second);
          }
        }
      }
  };
};

#endif
