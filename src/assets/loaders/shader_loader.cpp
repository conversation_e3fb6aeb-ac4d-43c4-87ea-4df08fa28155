#include "shader_loader.hpp"

// C++ standard library
#include <iostream>
#include <memory>
#include <string>

// Local includes
#include "../../vfs/vfs.hpp"
#include "../asset_data_types.hpp"

namespace IronFrost {
  ShaderLoader::ShaderLoader(IVFS & vfs) : m_vfs{vfs}
  {}

  std::unique_ptr<ShaderData> ShaderLoader::loadShader(const std::string & path) {
    auto shaderData = std::make_unique<ShaderData>(ShaderData{
      .vertexShader = m_vfs.readFile(path + ".vs"),
      .fragmentShader = m_vfs.readFile(path + ".fs")
    });

    return shaderData;
  }
}
