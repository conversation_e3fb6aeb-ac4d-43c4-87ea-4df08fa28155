#include "audio_engine.hpp"

// Local includes
#include "openal/openal_audio_engine.hpp"

namespace IronFrost {
  bool IAudioEngine::isAudioClipLoaded(const StringID& id) const {
    return m_audioClips.find(id) != m_audioClips.end();
  }

  std::unique_ptr<IAudioEngine> IAudioEngine::create(AUDIO_ENGINE_TYPE type) {
    switch (type) {
      case AUDIO_ENGINE_TYPE::OPENAL_ENGINE:
        return std::make_unique<OpenALAudioEngine>();
      default:
        throw std::runtime_error("Unknown audio engine type");
    }
  }

}
