#ifndef __IF__AUDIO_ENGINE_HPP
#define __IF__AUDIO_ENGINE_HPP

// C++ standard library
#include <memory>
#include <string>
#include <unordered_map>

// Local includes
#include "../utils/string_id.hpp"
#include "../assets/asset_data_types.hpp"

namespace IronFrost {
  enum class AUDIO_ENGINE_TYPE {
    OPENAL_ENGINE
  };

  class IAudioEngine {
    protected:
      std::unordered_map<StringID, std::unique_ptr<AudioData>> m_audioClips;

    public:
      explicit IAudioEngine() = default;
      virtual ~IAudioEngine() = default;

      virtual bool initialize() = 0;
      virtual void shutdown() = 0;

      virtual bool loadAudioClip(const StringID& id, const AudioData& audioData) = 0;
      virtual void unloadAudioClip(const StringID& id) = 0;
      virtual bool isAudioClipLoaded(const StringID& id) const;

      virtual void playMusic(const StringID& clipId, bool looping = false) = 0;
      virtual void stopMusic() = 0;
      virtual void pauseMusic() = 0;
      virtual void resumeMusic() = 0;

      virtual void setMusicVolume(float volume) = 0;
      virtual float getMusicVolume() const = 0;

      virtual bool isMusicPlaying() const = 0;
      virtual bool isMusicPaused() const = 0;

      static std::unique_ptr<IAudioEngine> create(AUDIO_ENGINE_TYPE type);
  };
}

#endif
