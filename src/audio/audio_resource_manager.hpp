#ifndef __IF__AUDIO_RESOURCE_MANAGER_HPP
#define __IF__AUDIO_RESOURCE_MANAGER_HPP

// Local includes
#include "../events/event_dispatcher.hpp"
#include "../utils/string_id.hpp"
#include "audio_engine.hpp"

namespace IronFrost {
  // Forward declarations
  class AssetsManager;
  class AssetsLoader;
  struct AudioData;

  /**
   * AudioResourceManager handles loading and management of audio resources.
   * It coordinates between the AssetsManager (file loading) and AudioEngine (playback).
   */
  class AudioResourceManager {
    private:
      AssetsManager& m_assetsManager;
      IAudioEngine& m_audioEngine;
      EventDispatcher& m_eventDispatcher;

    public:
      AudioResourceManager(AssetsManager& assetsManager, IAudioEngine& audioEngine, EventDispatcher& eventDispatcher);
      ~AudioResourceManager() = default;

      // Resource loading
      void loadAllAudio(const AssetsLoader& assetsLoader);
      void unloadAllAudio();
  };
}

#endif
