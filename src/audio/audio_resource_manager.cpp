#include "audio_resource_manager.hpp"

// C++ standard library
#include <iostream>

// Local includes
#include "../assets/assets.hpp"
#include "../assets/assets_loader.hpp"
#include "../assets/asset_data_types.hpp"
#include "../renderer/resource_events.hpp"
#include "../renderer/resource_type.hpp"

namespace IronFrost {
  AudioResourceManager::AudioResourceManager(AssetsManager& assetsManager, IAudioEngine& audioEngine, EventDispatcher& eventDispatcher)
    : m_assetsManager(assetsManager), m_audioEngine(audioEngine), m_eventDispatcher(eventDispatcher) {}

  void AudioResourceManager::loadAllAudio(const AssetsLoader& assetsLoader) {
    std::cout << "Loading all audio resources..." << std::endl;

    assetsLoader.loadAudio(m_assetsManager,
      [&](const StringID _name, const AudioData& _audioData) {

        if (m_audioEngine.loadAudioClip(_name, _audioData)) {
          m_eventDispatcher.dispatchAsync<ResourceCreatedEvent>(_name, ResourceType::AUDIO);
        }
      });

    std::cout << "All audio resources loaded" << std::endl;
  }

  void AudioResourceManager::unloadAllAudio() {
    std::cout << "Unloading all audio resources..." << std::endl;

    m_audioEngine.stopMusic();

    std::cout << "All audio resources unloaded" << std::endl;
  }
}
