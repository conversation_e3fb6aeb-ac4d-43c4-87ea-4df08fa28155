#ifndef __IF__SCENE_CONTEXT_HPP
#define __IF__SCENE_CONTEXT_HPP

namespace IronFrost {
  class AssetsManager;
  class EventDispatcher;
  class IRenderer;
  class IScriptEngine;
  class IVFS;

  struct SceneContext {
    AssetsManager& assetsManager;

    IVFS& vfs;
    IRenderer& renderer;
    IScriptEngine* scriptEngine;  // Optional - can be nullptr if scripts disabled

    SceneContext(AssetsManager& assetsManager, IVFS& vfs, IRenderer& renderer, IScriptEngine* scriptEngine = nullptr)
      : assetsManager(assetsManager), vfs(vfs), renderer(renderer), scriptEngine(scriptEngine) {}
  };
}

#endif
