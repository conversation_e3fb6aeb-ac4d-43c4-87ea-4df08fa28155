#ifndef __IF__SPATIAL_PARTITIONING_HPP
#define __IF__SPATIAL_PARTITIONING_HPP

// C++ standard library
#include <memory>

namespace IronFrost {
  class SceneNode;

  enum class SPATIAL_PARTITIONING_TYPE {
    OCTREE
  };

  class ISpatialPartitioning {
    public:
      virtual ~ISpatialPartitioning() = default;

      virtual void insert(SceneNode* node) = 0;
      virtual void remove(SceneNode* node) = 0;

      static std::unique_ptr<ISpatialPartitioning> create(SPATIAL_PARTITIONING_TYPE spatialPartitioningType);
  };
}

#endif
