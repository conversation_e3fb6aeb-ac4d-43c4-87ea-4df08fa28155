#ifndef __IF__OCTREE_HPP
#define __IF__OCTREE_HPP

// C++ standard library
#include <memory>
#include <vector>

// Local includes
#include "../spatial_partitioning.hpp"
#include "octree_node.hpp"

namespace IronFrost {
  class Octree : public ISpatialPartitioning {
    private:
      std::unique_ptr<OctreeNode> m_root;

    public:
      Octree() = default;
      ~Octree() override = default;

      void insert(SceneNode* node) override;
      void remove(SceneNode* node) override;
  };
}

#endif
