#ifndef __IF__OCTREE_NODE_HPP
#define __IF__OCTREE_NODE_HPP

// C++ standard library
#include <memory>
#include <vector>
#include <algorithm>

// Local includes
#include "../../../utils/collision_math.hpp"

namespace IronFrost {
  class OctreeNode {
    private:
      CollisionMath::AABB m_bounds;
      int m_depth;

      std::vector<SceneNode*> m_objects;
      std::unique_ptr<OctreeNode> children[8];

    public:
      static constexpr int MaxObjectsPerNode = 8;
      static constexpr int MaxDepth = 8;

      OctreeNode(const CollisionMath::AABB& bounds, int depth) : m_bounds(bounds), m_depth(depth) {}
      ~OctreeNode() = default;

      bool insert(SceneNode* node) {
        m_objects.push_back(node);
        return false;
      }

      bool remove(SceneNode* node) {
        m_objects.erase(std::remove(m_objects.begin(), m_objects.end(), node), m_objects.end());
        return false;
      }
  };
}

#endif
