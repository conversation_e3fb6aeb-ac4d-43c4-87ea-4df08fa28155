#ifndef __IF__SCENE_MANAGER_HPP
#define __IF__SCENE_MANAGER_HPP

// C++ standard library
#include <atomic>
#include <memory>
#include <string>
#include <unordered_map>

// Third-party libraries
#include <nlohmann/json.hpp>

// Local includes
#include "../scripts/scene_script_handler.hpp"
#include "../utils/string_id.hpp"
#include "scene.hpp"

using json = nlohmann::json;

namespace IronFrost
{
  class AssetsManager;
  class EventDispatcher;
  struct GameContext;
  class IRenderer;
  class IScriptEngine;
  class IVFS;
  class ScriptEngine;

  class SceneManager {
    private:
      std::unique_ptr<SceneContext> m_sceneContext;
      std::unordered_map<StringID, std::unique_ptr<IScene>> m_scenes;

      IScene* m_currentScene{nullptr};

      std::unique_ptr<LoadingScene> m_loadingScene{nullptr};

      std::atomic<bool> m_isSceneLoaded{false};

    public:
      SceneManager(const SceneManager &) = delete;
      SceneManager(IVFS& _vfs, AssetsManager& assetsManager, IRenderer& renderer, IScriptEngine* scriptEngine = nullptr);

      ~SceneManager();

      void loadScenesFromFile(const std::string& path);
      void createScene(const StringID& sceneName, const std::string& scenePath);

      void switchToScene(const StringID& sceneName);

      void updateCurrentScene(float deltaTime, GameContext& gameContext);
      void renderCurrentScene() const;
      void executeScenePerFrameScripts(float deltaTime);

      StringID getCurrentSceneName() const;
    };
}

#endif
