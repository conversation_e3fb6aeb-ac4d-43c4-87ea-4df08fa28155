#include "camera.hpp"

// C standard library
#include <cmath>

// C++ standard library
#include <iostream>

// Third-party libraries
#define GLM_ENABLE_EXPERIMENTAL
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtx/rotate_vector.hpp>

namespace IronFrost {
  void Camera::updateViewMatrix() {
    m_viewMatrix = glm::lookAt(m_position, m_position + m_direction, m_up);
  }

  void Camera::updateProjectionMatrix() {
    m_projectionMatrix = glm::perspective(glm::radians(m_fov), m_aspectRatio, m_nearPlane, m_farPlane);
  }

  void Camera::updateVectors() {
    m_right = glm::normalize(glm::cross(m_direction, m_up));
    m_up = glm::normalize(glm::cross(m_right, m_direction));
  }

  void Camera::setPosition(const glm::vec3& position) {
    m_position = position;
    updateViewMatrix();
  }

  const glm::vec3& Camera::getPosition() const {
    return m_position;
  }

  void Camera::setDirection(const glm::vec3& direction) {
    m_direction = glm::normalize(direction);
    updateVectors();
    updateViewMatrix();
  }

  void Camera::setUp(const glm::vec3& up) {
    m_up = glm::normalize(up);
    updateVectors();
    updateViewMatrix();
  }

  void Camera::setFov(float fov) {
    m_fov = fov;
    updateProjectionMatrix();
  }

  void Camera::setAspectRatio(float aspectRatio) {
    m_aspectRatio = aspectRatio;
    updateProjectionMatrix();
  }

  void Camera::setNearPlane(float near) {
    m_nearPlane = near;
    updateProjectionMatrix();
  }

  void Camera::setFarPlane(float far) {
    m_farPlane = far;
    updateProjectionMatrix();
  }

  float Camera::getCollisionRadius() const {
    return m_collisionRadius;
  }

  void Camera::setCollisionRadius(float radius) {
    m_collisionRadius = radius;
  }

  void Camera::moveForward(float speed, float deltaTime) {
    m_position += m_direction * speed * deltaTime;
    updateViewMatrix();
  }

  void Camera::moveBackward(float speed, float deltaTime) {
    moveForward(-speed, deltaTime);
  }

  void Camera::moveLeft(float speed, float deltaTime) {
    moveRight(-speed, deltaTime);
  }

  void Camera::moveRight(float speed, float deltaTime) {
    m_position += m_right * speed * deltaTime;
    updateViewMatrix();
  }

  void Camera::turnLeft(float speed, float deltaTime) {
    float angle = glm::radians(speed * deltaTime);

    m_direction = glm::normalize(glm::rotate(m_direction, angle, m_up));

    updateVectors();
    updateViewMatrix();
  }

  void Camera::turnRight(float speed, float deltaTime) {
    turnLeft(-speed, deltaTime);
  }

  void Camera::turnUp(float speed, float deltaTime) {
    float angle = glm::radians(speed * deltaTime);

    glm::vec3 newDirection = glm::normalize(glm::rotate(m_direction, angle, m_right));
    if (std::abs(glm::dot(newDirection, m_up)) < 0.99F) {
      m_direction = newDirection;

      updateVectors();
      updateViewMatrix();
    }
  }

  void Camera::turnDown(float speed, float deltaTime) {
    turnUp(-speed, deltaTime);
  }

  glm::vec3 Camera::calculateForwardMovement(float speed, float deltaTime) const {
    return m_position + m_direction * speed * deltaTime;
  }

  glm::vec3 Camera::calculateBackwardMovement(float speed, float deltaTime) const {
    return calculateForwardMovement(-speed, deltaTime);
  }

  glm::vec3 Camera::calculateLeftMovement(float speed, float deltaTime) const {
    return calculateRightMovement(-speed, deltaTime);
  }

  glm::vec3 Camera::calculateRightMovement(float speed, float deltaTime) const {
    return m_position + m_right * speed * deltaTime;
  }

  void Camera::setPositionIfValid(const glm::vec3& newPosition) {
    m_position = newPosition;
    updateViewMatrix();
  }

  void Camera::mouseLookYaw(float angle) {
    glm::vec3 worldUp(0.0f, 1.0f, 0.0f);
    m_direction = glm::normalize(glm::rotate(m_direction, angle, worldUp));

    m_right = glm::normalize(glm::cross(m_direction, worldUp));
    m_up = worldUp;

    updateViewMatrix();
  }

  void Camera::mouseLookPitch(float angle) {
    glm::vec3 newDirection = glm::normalize(glm::rotate(m_direction, angle, m_right));
    glm::vec3 worldUp(0.0f, 1.0f, 0.0f);

    if (std::abs(glm::dot(newDirection, worldUp)) < 0.99f) {
      m_direction = newDirection;

      m_up = worldUp;
      m_right = glm::normalize(glm::cross(m_direction, m_up));

      updateViewMatrix();
    }
  }

  const glm::mat4& Camera::getViewMatrix() const {
    return m_viewMatrix;
  }

  const glm::mat4& Camera::getProjectionMatrix() const {
    return m_projectionMatrix;
  }
}
