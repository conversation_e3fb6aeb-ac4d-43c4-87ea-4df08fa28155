#ifndef __IF__SCENE_RENDERER_HPP
#define __IF__SCENE_RENDERER_HPP

// C++ standard library
#include <iostream>

// Third-party libraries
#define GLM_ENABLE_EXPERIMENTAL
#include <glm/glm.hpp>
#include <glm/gtx/string_cast.hpp>

// Local includes
#include "../gui/gui.hpp"
#include "../renderer/gui_renderer.hpp"
#include "../renderer/instance_data.hpp"
#include "../renderer/postprocess_effect.hpp"
#include "../renderer/renderables_manager.hpp"
#include "../renderer/renderer.hpp"
#include "camera.hpp"
#include "scene_graph/scene_graph.hpp"

namespace IronFrost {
  class SceneRenderer {
    private:
      IRenderer& m_renderer;

      PostprocessEffect& m_postprocessEffect;
      ShaderUniforms m_postprocessUniforms{};

      void submitRenderableModel(RenderableModelID modelID, const glm::mat4& transform) {
        const RenderablesManager& manager = m_renderer.getRenderablesManager();
        const RenderableModel* model = manager.getRenderableModel(modelID);

        if (!model) return;

        submitRenderableModelNode(model->root, transform);
      }

      void submitRenderableModelNode(const RenderableModel::Node& node, const glm::mat4& transform) {
        glm::mat4 instanceTransform = transform * node.transform;

        for (const RenderableObjectID& objectID : node.objects) {
          m_renderer.submitRenderableObject(objectID, InstanceData{instanceTransform});
        }

        for (const RenderableModel::Node& child : node.children) {
          submitRenderableModelNode(child, instanceTransform);
        }
      }

      void submitSceneObject(const SceneObject& object, const glm::mat4& transform) {
        m_renderer.submitRenderableObject(
          object.renderableObjectID,
          InstanceData{
            .transform = transform,
            .uvTiling = object.uvTiling}
        );
      }

    public:
      SceneRenderer(IRenderer& renderer) : 
        m_renderer(renderer),
        m_postprocessEffect(m_renderer.getPostprocessEffect(StringID("postprocess::default")))
      {}

      void setPostprocessUniforms(const ShaderUniforms& uniforms) {
        m_postprocessUniforms = uniforms;
        m_postprocessEffect.uniforms = m_postprocessUniforms;
      }

      void setPostprocessEffect(PostprocessEffect& postprocessEffect) {
        m_postprocessEffect = postprocessEffect;
        m_postprocessEffect.uniforms = m_postprocessUniforms;
      }

      void setPostprocessEffect(StringID effectName) {
        setPostprocessEffect(m_renderer.getPostprocessEffect(effectName));
      }

      void renderSceneGraph(ISceneGraph& sceneGraph, Camera& camera) {
        m_renderer.clearRenderQueue();
        m_renderer.setViewProjection(camera.getViewMatrix(), camera.getProjectionMatrix());

        sceneGraph.traverse([&](const SceneNode& node, const glm::mat4& transform) {
          if (!node.visible) return;

          if (node.sceneObject) {
            submitSceneObject(node.sceneObject.value(), transform);
          }

          if (node.sceneModel) {
            submitRenderableModel(node.sceneModel->renderableModelID, transform);
          }

          if (node.sceneLight) {
            RenderableLight light{
              RenderableLight::Type::Point, 
              node.getPosition(),
              node.sceneLight->color, 
              node.sceneLight->intensity, 
              node.sceneLight->constant, 
              node.sceneLight->linear, 
              node.sceneLight->quadratic
            };

            m_renderer.submitRenderableLight(light);
          }
        });

        m_renderer.withRenderState({.depthTest = true, .cullFace = true, .blend = true}, [&]() {
          m_renderer.render();
        });
      };

      void renderDebugSceneGraph(ISceneGraph& sceneGraph, Camera& camera) {
        const IResourceManager& resourceManager = m_renderer.getResourceManager();

        MeshHandle meshHandle = resourceManager.getMesh(FallbackResources::DEBUG_CUBE_NAME);
        ShaderHandle shaderHandle = resourceManager.getShader(DefaultShaders::DEFAULT_DEBUG_SHADER_NAME);

        m_renderer.clearRenderQueue();
        m_renderer.setViewProjection(camera.getViewMatrix(), camera.getProjectionMatrix());

        sceneGraph.traverse([&](const SceneNode& node, const glm::mat4& transform) {
          CollisionMath::AABB localAABB{glm::vec3(0.0f), glm::vec3(0.0f)};

          if (node.sceneObject) {
            const SceneObject& object = node.sceneObject.value();
            localAABB = object.bounds;
          }

          if (node.sceneModel) {
            const SceneModel& model = node.sceneModel.value();
            localAABB = model.bounds;
          }

          CollisionMath::AABB worldAABB = CollisionMath::transformAABB(localAABB, transform);

          std::cout << "local: " << glm::to_string(localAABB.min) << " → " << glm::to_string(localAABB.max) << std::endl;
          std::cout << "world: " << glm::to_string(worldAABB.min) << " → " << glm::to_string(worldAABB.max) << std::endl;

          glm::vec3 center = worldAABB.getCenter();
          glm::vec3 size = worldAABB.getSize();
          glm::mat4 modelTransform = glm::translate(glm::mat4(1.0f), center) * glm::scale(glm::mat4(1.0f), size);

          m_renderer.submitRenderableObject(
            RenderableObject{shaderHandle, meshHandle, {}, {}}, 
            InstanceData{.transform = modelTransform}
          );
        });

        m_renderer.withRenderState({.depthTest = true, .cullFace = false, .blend = false, .wireframe = true}, [&]() {
          m_renderer.render();
        });
      }

      void render(ISceneGraph& sceneGraph, Camera& camera, GUI& gui) {
        m_renderer.renderToFramebuffer([&]() {
          renderSceneGraph(sceneGraph, camera);
          renderDebugSceneGraph(sceneGraph, camera);
        });

        m_renderer.renderPostprocessEffect(m_postprocessEffect);
        m_renderer.getGUIRenderer().render(gui);
      }
  };
}

#endif
