#ifndef __IF__SCENE_ASSETS_HPP
#define __IF__SCENE_ASSETS_HPP

// C standard library
#include <cstddef>

// C++ standard library
#include <functional>
#include <iostream>
#include <string>
#include <tuple>
#include <unordered_set>

// Local includes
#include "../assets/assets_loader.hpp"
#include "../assets/assets_manifest.hpp"
#include "../renderer/renderer.hpp"
#include "../renderer/resource_manager.hpp"
#include "../services/resource_orchestrator.hpp"
#include "../services/service_locator.hpp"
#include "../utils/string_id.hpp"
#include "scene_context.hpp"

namespace std {
  template <>
  struct hash<std::tuple<IronFrost::StringID, IronFrost::ResourceType>> {
      std::size_t operator()(const std::tuple<IronFrost::StringID, IronFrost::ResourceType>& key) const {
          const auto& [id, type] = key;
          std::size_t h1 = std::hash<IronFrost::StringID>{}(id);
          std::size_t h2 = std::hash<IronFrost::ResourceType>{}(type);
          return h1 ^ (h2 << 1);
      }
  };
}

namespace IronFrost {
  class SceneAssets {
    private:
      const SceneContext& m_sceneContext;
      IResourceManager& m_resourceManager;

      std::unordered_set<std::tuple<StringID, ResourceType>> m_loadedAssets;

      bool m_isLoaded{false};

      EventListenerHandle m_resourceCreatedHandle;

    public:
      SceneAssets(SceneContext& sceneContext) : 
        m_sceneContext(sceneContext),
        m_resourceManager(sceneContext.renderer.getResourceManager())
      {}

      void load(const std::string& path) {
        auto manifest = AssetsManifest::fromFile(m_sceneContext.vfs, path); 
        auto assetsLoader = AssetsLoader::fromFile(m_sceneContext.vfs, path);

        m_resourceCreatedHandle = ServiceLocator::getService<EventDispatcher>().registerListener<ResourceCreatedEvent>(
          [&](const ResourceCreatedEvent& _event) {
            m_loadedAssets.insert(std::make_tuple(_event.name(), _event.type())); 
            std::cout << "Scene Resource Loaded: " << StringID::getString(_event.name()) << std::endl;
          }
        );
    
        // Use the ResourceOrchestrator service for loading resources
        ResourceOrchestrator& resourceOrchestrator = ServiceLocator::getService<ResourceOrchestrator>();
        resourceOrchestrator.loadResources(*assetsLoader);

        m_isLoaded = true;
      }

      void unload() {
        ServiceLocator::getService<EventDispatcher>().unregisterListener<ResourceCreatedEvent>(m_resourceCreatedHandle);
    
        m_isLoaded = false;

        for (auto& [name, type] : m_loadedAssets) {
          m_resourceManager.destroyResource(name, type);
        }
      }

      bool isLoaded() const {
        return m_isLoaded;
      }
  };
}

#endif
