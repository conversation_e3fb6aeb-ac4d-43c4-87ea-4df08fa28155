#ifndef __IF__SCENE_LIST_HPP
#define __IF__SCENE_LIST_HPP

// C++ standard library
#include <unordered_map>

// Local includes
#include "../../utils/string_id.hpp"
#include "scene_graph.hpp"

namespace IronFrost {
  class SceneList : public ISceneGraph {
    private:
      std::unordered_map<StringID, SceneNode> m_nodes;

    public:
      SceneList() = default;
      ~SceneList() = default;

      void traverse(std::function<void(SceneNode &, const glm::mat4&)> callback) override;
      void insert(StringID name, SceneNode&& object) override;

      SceneNode& operator[](StringID name) override;
  };
}

#endif
