#ifndef __IF__SCENE_GRAPH_HPP
#define __IF__SCENE_GRAPH_HPP

// C++ standard library
#include <functional>
#include <unordered_map>
#include <vector>

// Third-party libraries
#include <glm/gtc/quaternion.hpp>

// Local includes
#include "../../utils/string_id.hpp"
#include "scene_entities.hpp"
#include "scene_node.hpp"

namespace IronFrost {

  enum class SCENE_GRAPH_TYPE {
    LIST
  };

  class ISceneGraph {
    public:
      virtual ~ISceneGraph() = default;

      virtual void traverse(std::function<void(SceneNode &, const glm::mat4&)> callback) = 0;
      virtual void insert(StringID name, SceneNode&& object) = 0;

      virtual SceneNode& operator[](StringID name) = 0;

      static std::unique_ptr<ISceneGraph> create(SCENE_GRAPH_TYPE sceneGraphType);
  };
}

#endif
