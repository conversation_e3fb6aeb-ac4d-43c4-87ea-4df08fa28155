#include "scene_list.hpp"

// C++ standard library
#include <functional>

namespace IronFrost {
  void SceneList::traverse(std::function<void(SceneNode&, const glm::mat4&)> callback) {
    for (auto& [name, sceneNode] : m_nodes) {
      sceneNode.traverse(glm::mat4(1.0F), callback);
    }
  }

  void SceneList::insert(StringID name, SceneNode&& object) {
    m_nodes[name] = std::move(object);
  }

  SceneNode& SceneList::operator[](StringID name) {
    return m_nodes[name];
  }
}
