#ifndef __IF__APPLICATION_BUILDER_HPP
#define __IF__APPLICATION_BUILDER_HPP

// C++ standard library
#include <memory>
#include <string>

// Local includes
#include "../renderer/renderer.hpp"
#include "../scripts/script_engine.hpp"
#include "../window/window.hpp"

namespace IronFrost {
  class Application;
  class Config;

  class ApplicationBuilder {
    private:
      // Window configuration
      int m_windowWidth{1280};
      int m_windowHeight{720};
      std::string m_windowTitle{"IronFrost"};
      WINDOW_LIBRARY m_windowLibrary{WINDOW_LIBRARY::GLFW};

      // Renderer configuration
      RENDERER_TYPE m_rendererType{RENDERER_TYPE::OPENGL};

      // Script configuration
      SCRIPT_ENGINE_TYPE m_scriptEngineType{SCRIPT_ENGINE_TYPE::LUA};

      // Scene configuration
      std::string m_scenesConfigPath{"scenes/config.json"};
      std::string m_defaultSceneName{"default"};

      // Flags for optional components
      bool m_enableConsole{true};
      bool m_enableScripts{true};
      bool m_enableScenes{true};

      // Helper methods for build process
      void createCoreServices(Application& app);
      Config loadGlobalConfiguration(Application& app);
      void createGameContext(Application& app);
      void createConsole(Application& app);
      void createScriptEngine(Application& app, const Config& config);
      void createSceneManager(Application& app);

    public:
      ApplicationBuilder() = default;

      // Window configuration
      ApplicationBuilder& withWindow(int width, int height, const std::string& title);
      ApplicationBuilder& withWindowLibrary(WINDOW_LIBRARY library);

      // Renderer configuration
      ApplicationBuilder& withRenderer(RENDERER_TYPE type);

      // Script configuration
      ApplicationBuilder& withScriptEngine(SCRIPT_ENGINE_TYPE type);
      ApplicationBuilder& withoutScripts();

      // Scene configuration
      ApplicationBuilder& withScenes(const std::string& configPath, const std::string& defaultScene = "default");
      ApplicationBuilder& withoutScenes();

      // Console configuration
      ApplicationBuilder& withoutConsole();

      // Build the application
      std::unique_ptr<Application> build();
  };
}

#endif
