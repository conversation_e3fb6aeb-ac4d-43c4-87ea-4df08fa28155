#ifndef __IF__OPENGL_POSTPROCESS_MANAGER_HPP
#define __IF__OPENGL_POSTPROCESS_MANAGER_HPP

// Local includes
#include "../postprocess_manager.hpp"

namespace IronFrost {
  class OpenGLPostprocessManager : public IPostprocessManager {
    public:
      OpenGLPostprocessManager(IMultipassFramebuffer& multipassFramebuffer, IResourceManager& resourceManager) :
        IPostprocessManager(multipassFramebuffer, resourceManager)
      {}

      virtual ~OpenGLPostprocessManager() = default;

      void renderPostprocessEffect(const PostprocessEffect& postprocessEffect) override {
        for (auto it = postprocessEffect.shaderHandles.begin(); it != postprocessEffect.shaderHandles.end(); ++it) {
          const ShaderHandle& shaderHandle = *it;

          setRenderToFramebuffer(mmultipassFramebuffer->getWriteFramebuffer());

          glUseProgram(shaderHandle.programID);
          setUniforms(shaderHandle, postprocessEffect.uniforms);

          if (std::next(it) == postprocessEffect.shaderHandles.end()) {
            renderFramebufferToScreen(mmultipassFramebuffer->getReadFramebuffer());
          } else {
            m_screen->render(m_multipassFramebuffer->getReadFramebuffer());
            m_multipassFramebuffer->swap();
          }
        }
      }
  };
}

#endif
