#include "opengl_renderer.hpp"

// C standard library
#include <cassert>

// C++ standard library
#include <exception>
#include <functional>
#include <iostream>
#include <memory>
#include <stdexcept>
#include <string>
#include <tuple>
#include <type_traits>

// Third-party libraries
#include <glad/glad.h>
#include <glm/gtc/type_ptr.hpp>

// Local includes
#include "../../assets/asset_data_types.hpp"
#include "../../events/event_dispatcher.hpp"
#include "../../services/service_locator.hpp"
#include "../../window/window.hpp"
#include "../instance_data.hpp"
#include "../postprocess_effect.hpp"
#include "../renderables_manager.hpp"
#include "../shader_uniforms.hpp"
#include "opengl_default_shaders.hpp"
#include "opengl_multipass_framebuffer.hpp"
#include "opengl_resource_manager.hpp"
#include "opengl_screen.hpp"
#include "opengl_shader.hpp"

namespace IronFrost {
  bool OpenGLRenderer::hasUniform(const ShaderHandle& shaderHandle, const std::string& name) {
    int location = std::get<0>(shaderHandle.getUniformLocation(name));
    return location >= 0;
  }

  int OpenGLRenderer::getUniformLocation(const ShaderHandle& shaderHandle, const std::string& name) {
    return std::get<0>(shaderHandle.getUniformLocation(name));
  }

  void OpenGLRenderer::setUniform(const ShaderHandle& shaderHandle, const std::string& name, const UniformValue& value) {
    int location = std::get<0>(shaderHandle.getUniformLocation(name));
    if (location < 0) {
      return;
    }

    std::visit([&](const auto& val) {
      using T = std::decay_t<decltype(val)>;

      if constexpr (std::is_same_v<T, float>) {
          glUniform1f(location, val);
      } else if constexpr (std::is_same_v<T, glm::vec2>) {
          glUniform2fv(location, 1, &val[0]);
      } else if constexpr (std::is_same_v<T, glm::vec3>) {
          glUniform3fv(location, 1, &val[0]);
      } else if constexpr (std::is_same_v<T, glm::vec4>) {
          glUniform4fv(location, 1, &val[0]);
      } else if constexpr (std::is_same_v<T, int>) {
          glUniform1i(location, val);
      } else if constexpr (std::is_same_v<T, glm::ivec2>) {
          glUniform2iv(location, 1, &val[0]);
      } else if constexpr (std::is_same_v<T, glm::ivec3>) {
          glUniform3iv(location, 1, &val[0]);
      } else if constexpr (std::is_same_v<T, glm::ivec4>) {
          glUniform4iv(location, 1, &val[0]);
      } else if constexpr (std::is_same_v<T, glm::mat3>) {
          glUniformMatrix3fv(location, 1, GL_FALSE, glm::value_ptr(val));
      } else if constexpr (std::is_same_v<T, glm::mat4>) {
          glUniformMatrix4fv(location, 1, GL_FALSE, glm::value_ptr(val));
      } else {
          std::cerr << "Unsupported uniform type for: " << name << '\n';
      }

    }, value);
  }

  void OpenGLRenderer::setUniforms(const ShaderHandle& shaderHandle, const ShaderUniforms& shaderUniforms) {
    for (const auto& [name, value] : shaderUniforms) {
      setUniform(shaderHandle, name, value);
    }
  }

  void OpenGLRenderer::bindBlinnPhongMaterial(const ShaderHandle& shaderHandle, const BlinnPhongMaterial& material) {
    setUniform(shaderHandle, "materialType", 0);

    setUniform(shaderHandle, "phongMaterial.ambient", material.ambient);
    setUniform(shaderHandle, "phongMaterial.diffuse", material.diffuse);
    setUniform(shaderHandle, "phongMaterial.specular", material.specular);
    setUniform(shaderHandle, "phongMaterial.shininess", material.shininess);

    glActiveTexture(GL_TEXTURE1);
    glBindTexture(GL_TEXTURE_2D, material.baseColor.textureID);
    setUniform(shaderHandle, "phongMaterial.baseColor", 1);
  }

  void OpenGLRenderer::bindPBRMaterial(const ShaderHandle& shaderHandle, const PBRMaterial& material) {
    setUniform(shaderHandle, "materialType", 1);

    glActiveTexture(GL_TEXTURE2);
    glBindTexture(GL_TEXTURE_2D_ARRAY, material.pbrTextureArray.textureID);
    setUniform(shaderHandle, "pbrTextureArray", 2);
  }

  void OpenGLRenderer::bindMaterial(const ShaderHandle& shaderHandle, const Material& material) {
    if (const auto* blinnPhongMaterial = std::get_if<BlinnPhongMaterial>(&material)) {
      bindBlinnPhongMaterial(shaderHandle, *blinnPhongMaterial);
    } else if (const auto* pbrMaterial = std::get_if<PBRMaterial>(&material)) {
      bindPBRMaterial(shaderHandle, *pbrMaterial);
    } else if (const auto* guiMaterial = std::get_if<GUIMaterial>(&material)) {
      glActiveTexture(GL_TEXTURE3);
      glBindTexture(GL_TEXTURE_2D, guiMaterial->baseColor.textureID);
      setUniform(shaderHandle, "ourTexture", 3);
    } else {
      throw std::runtime_error("Unknown material type");
    }
  }

  void OpenGLRenderer::createDefaultShaders() {
    m_resourceManager->createShader(
      DefaultShaders::DEFAULT_SHADER_NAME, 
      DefaultShaders::DEFAULT_VERTEX_SHADER, 
      DefaultShaders::DEFAULT_FRAGMENT_SHADER);
      
    m_resourceManager->createShader(
      DefaultShaders::DEFAULT_POSTPROCESS_SHADER_NAME, 
      DefaultShaders::DEFAULT_POSTPROCESS_VERTEX_SHADER, 
      DefaultShaders::DEFAULT_POSTPROCESS_FRAGMENT_SHADER);

    m_resourceManager->createShader(
      DefaultShaders::DEFAULT_GUI_SHADER_NAME,
      DefaultShaders::DEFAULT_GUI_VERTEX_SHADER,
      DefaultShaders::DEFAULT_GUI_FRAGMENT_SHADER);
    
    m_resourceManager->createShader(
      DefaultShaders::DEFAULT_TEXT_SHADER_NAME,
      DefaultShaders::DEFAULT_TEXT_VERTEX_SHADER,
      DefaultShaders::DEFAULT_TEXT_FRAGMENT_SHADER);

    m_resourceManager->createShader(
      DefaultShaders::DEFAULT_DEBUG_SHADER_NAME,
      DefaultShaders::DEFAULT_DEBUG_VERTEX_SHADER,
      DefaultShaders::DEFAULT_DEBUG_FRAGMENT_SHADER);
  }

  void OpenGLRenderer::setRenderToFramebuffer(const FramebufferHandle& framebufferHandle) {
    glBindFramebuffer(GL_FRAMEBUFFER, framebufferHandle.framebufferID);
    glClearColor(0.1F, 0.1F, 0.1F, 1.0F);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
  }

  void OpenGLRenderer::setRenderTargetDefault() {
    glBindFramebuffer(GL_FRAMEBUFFER, 0);
  }

  void OpenGLRenderer::renderFramebufferToScreen(const FramebufferHandle& framebufferHandle) {
    setRenderTargetDefault();

    glClearColor(0.1F, 0.1F, 0.1F, 1.0F);
    glClear(GL_COLOR_BUFFER_BIT);

    m_screen->render(framebufferHandle);
  }

  void OpenGLRenderer::setPointLightUniforms(const ShaderHandle& shaderHandle) {
    const int numPointLights = m_renderableLights.size() > 8 ? 8 : static_cast<int>(m_renderableLights.size());
    setUniform(shaderHandle, "numPointLights", numPointLights);

    for (int i = 0; i < numPointLights; ++i) {
      const auto& renderableLight = m_renderableLights[i];

      setUniform(shaderHandle, "pointLights[" + std::to_string(i) + "].position", renderableLight.position);
      setUniform(shaderHandle, "pointLights[" + std::to_string(i) + "].color", renderableLight.color);
      setUniform(shaderHandle, "pointLights[" + std::to_string(i) + "].intensity", renderableLight.intensity);
      setUniform(shaderHandle, "pointLights[" + std::to_string(i) + "].constant", renderableLight.constant);
      setUniform(shaderHandle, "pointLights[" + std::to_string(i) + "].linear", renderableLight.linear);
      setUniform(shaderHandle, "pointLights[" + std::to_string(i) + "].quadratic", renderableLight.quadratic);
    }
  }

  glm::mat3 OpenGLRenderer::calculateNormalMatrix(const glm::mat4& modelMatrix) {
    glm::mat3 normalMatrix{1.0F};
    glm::mat3 model{modelMatrix};

    float det = glm::determinant(model);
    if (glm::abs(det) > 1e-6F) {
      normalMatrix = glm::transpose(glm::inverse(model));
    }

    return normalMatrix;
  }

  OpenGLRenderer::OpenGLRenderer(IWindow& window) :
    IRenderer(window)
  {
    m_resourceManager = std::make_unique<OpenGLResourceManager>();
    m_renderablesManager = std::make_unique<RenderablesManager>(*m_resourceManager);
    m_multipassFramebuffer = std::make_unique<OpenGLMultipassFramebuffer>(m_window.getWidth(), m_window.getHeight());
    m_screen = std::make_unique<OpenGLScreen>(m_window.getWidth(), m_window.getHeight());
    
    std::cout << "OpenGL Version: " << glGetString(GL_VERSION) << '\n';

    glGetIntegerv(GL_MAX_TEXTURE_IMAGE_UNITS, &m_maxTextureUnits);
    std::cout << "Max Texture Units: " << m_maxTextureUnits << '\n';

    createDefaultShaders();

    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);  

    glEnable(GL_CULL_FACE);
    // glEnable(GL_FRAMEBUFFER_SRGB);

    ServiceLocator::getService<EventDispatcher>().registerListener<WindowResizeEvent>(
      [&](const WindowResizeEvent &event) {
        m_screen->resize(event.getWidth(), event.getHeight());
      });
  }

  OpenGLRenderer::~OpenGLRenderer() = default;

  std::unique_ptr<OpenGLRenderer> OpenGLRenderer::create(IWindow& window) {
    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast) - Required for OpenGL function loading
    if (gladLoadGLLoader(reinterpret_cast<GLADloadproc>(window.getRawHandle())) == 0) {
      return nullptr;
    }

    return std::unique_ptr<OpenGLRenderer>(new OpenGLRenderer(window));
  }

  void OpenGLRenderer::useShader(unsigned int id) {
    m_currentShader = id;
    glUseProgram(m_currentShader);
  }

  void OpenGLRenderer::unbindShader() {
    m_currentShader = 0;
    glUseProgram(0);
  }

  void OpenGLRenderer::beginFrame() {
  }

  void OpenGLRenderer::endFrame() {
  }

  void OpenGLRenderer::render() {
    glm::mat4 viewProjection = m_projectionMatrix * m_viewMatrix;

    for (auto& [shaderHandle, objects] : m_rendererQueue) {
      if (objects.empty()) {
        continue;
      }

      useShader(shaderHandle.programID);

      setUniforms(shaderHandle, m_globalUniforms);
      setUniform(shaderHandle, "viewProjection", viewProjection);
      setPointLightUniforms(shaderHandle);

      for (const auto &obj : objects) {
        const RenderableObject &renderable = std::get<0>(obj);
        const InstanceData& instanceData = std::get<1>(obj);

        if (hasUniform(shaderHandle, "normalMatrix")) {
          setUniform(shaderHandle, "normalMatrix", calculateNormalMatrix(instanceData.transform));
        }

        setUniform(shaderHandle, "model", instanceData.transform);
        setUniform(shaderHandle, "uvTiling", instanceData.uvTiling);
        setUniforms(shaderHandle, renderable.uniforms);

        bindMaterial(shaderHandle, renderable.material);

        glBindVertexArray(renderable.meshHandle.VAO);

        #ifndef NDEBUG
          glValidateProgram(shaderHandle.programID);
          GLint status;
          glGetProgramiv(shaderHandle.programID, GL_VALIDATE_STATUS, &status);
          if (status == GL_FALSE) {
            char infoLog[512];
            glGetProgramInfoLog(shaderHandle.programID, 512, NULL, infoLog);
            std::cerr << "Shader validation failed: " << infoLog << '\n';
          }
        #endif

        if (renderable.meshHandle.numIndices > 0) {
          glDrawElements(GL_TRIANGLES, static_cast<GLsizei>(renderable.meshHandle.numIndices), GL_UNSIGNED_INT, nullptr);
        } else {
          glDrawArrays(GL_TRIANGLES, 0, static_cast<GLsizei>(renderable.meshHandle.numVertices));
        }
        #ifndef NDEBUG
          GLenum error = glGetError();
          if (error != GL_NO_ERROR) {
            std::cerr << "Rendering object: " << renderable.meshHandle.VAO << '\n';
            std::cerr << "OpenGL error: " << error << '\n';
          }
        #endif
        glBindVertexArray(0);
      }
    }

    #ifndef NDEBUG
      GLenum error = glGetError();
      if (error != GL_NO_ERROR) {
        std::cerr << "OpenGL error: " << error << '\n';
      }
    #endif
  }

  void OpenGLRenderer::renderToFramebuffer(std::function<void()> callback) {
    setRenderToFramebuffer(m_multipassFramebuffer->getWriteFramebuffer());
    callback();
    m_multipassFramebuffer->swap();
  }

  void OpenGLRenderer::renderPostprocessEffect(const PostprocessEffect& postprocessEffect) {
    for (auto it = postprocessEffect.shaderHandles.begin(); it != postprocessEffect.shaderHandles.end(); ++it) {
      const ShaderHandle& shaderHandle = *it;

      setRenderToFramebuffer(m_multipassFramebuffer->getWriteFramebuffer());

      useShader(shaderHandle.programID);
      setUniforms(shaderHandle, postprocessEffect.uniforms);

      if (std::next(it) == postprocessEffect.shaderHandles.end()) {
        renderFramebufferToScreen(m_multipassFramebuffer->getReadFramebuffer());
        unbindShader();
      } else {
        m_screen->render(m_multipassFramebuffer->getReadFramebuffer());
        m_multipassFramebuffer->swap();
      }
    }
  }

  void OpenGLRenderer::withRenderState(const RenderState& state, std::function<void()> func) {
    GLboolean prevDepthTest = glIsEnabled(GL_DEPTH_TEST);
    GLboolean prevCullFace = glIsEnabled(GL_CULL_FACE);
    GLboolean prevBlend = glIsEnabled(GL_BLEND);
    GLint prevPolygonMode[2];
    glGetIntegerv(GL_POLYGON_MODE, prevPolygonMode);

    state.depthTest ? glEnable(GL_DEPTH_TEST) : glDisable(GL_DEPTH_TEST);
    state.cullFace ? glEnable(GL_CULL_FACE) : glDisable(GL_CULL_FACE);
    state.blend ? glEnable(GL_BLEND) : glDisable(GL_BLEND);
    glPolygonMode(GL_FRONT_AND_BACK, state.wireframe ? GL_LINE : GL_FILL);

    func();

    prevDepthTest ? glEnable(GL_DEPTH_TEST) : glDisable(GL_DEPTH_TEST);
    prevCullFace ? glEnable(GL_CULL_FACE) : glDisable(GL_CULL_FACE);
    prevBlend ? glEnable(GL_BLEND) : glDisable(GL_BLEND);
    glPolygonMode(GL_FRONT_AND_BACK, prevPolygonMode[0]);
  }
}
