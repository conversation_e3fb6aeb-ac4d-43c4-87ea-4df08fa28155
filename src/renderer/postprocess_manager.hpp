#ifndef __IF__POSTPROCESS_MANAGER_HPP
#define __IF__POSTPROCESS_MANAGER_HPP

// C++ standard library
#include <algorithm>
#include <exception>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

// Local includes
#include "postprocess_effect.hpp"

namespace IronFrost {
  class IPostprocessManager {
    protected:
      std::unordered_map<StringID, PostprocessEffect> m_postprocessEffects;

      IMultipassFramebuffer& m_multipassFramebuffer;
      IResourceManager& m_resourceManager;

    public:
      explicit IPostprocessManager(IMultipassFramebuffer& multipassFramebuffer, IResourceManager& resourceManager) : 
        m_multipassFramebuffer(multipassFramebuffer),
        m_resourceManager(resourceManager)
      {}

      virtual ~IPostprocessManager() = default;

      PostprocessEffect& createPostprocessEffect(const StringID& name, const std::vector<std::string>& shaderNames) {
        std::vector<ShaderHandle> shaderHandles;

        std::transform(shaderNames.begin(), shaderNames.end(), std::back_inserter(shaderHandles),
                       [&](const std::string& _shaderName) { return m_resourceManager.getShader(StringID(_shaderName)); });
    
        PostprocessEffect effect{shaderHandles, {}};
    
        m_postprocessEffects.try_emplace(name, std::move(effect));
        return m_postprocessEffects.at(name);
      }

      PostprocessEffect& getPostprocessEffect(const StringID& name) {
        auto it = m_postprocessEffects.find(name);
        if (it != m_postprocessEffects.end()) {
          return it->second;
        }
    
        throw std::runtime_error("Postprocess effect not found: " + StringID::getString(name));
      }

      virtual void renderPostprocessEffect(const PostprocessEffect& postprocessEffect) = 0;
  };
}

#endif
