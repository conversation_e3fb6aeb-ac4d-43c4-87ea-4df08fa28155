#ifndef __IF__RESOURCE_MANAGER_HPP
#define __IF__RESOURCE_MANAGER_HPP

// C++ standard library
#include <exception>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

// Local includes
#include "../assets/asset_data_types.hpp"
#include "../events/event_dispatcher.hpp"
#include "../renderer/fallback_resources.hpp"
#include "../services/service_locator.hpp"
#include "gpu_handles.hpp"
#include "material.hpp"
#include "material_library.hpp"
#include "resource_events.hpp"
#include "resource_type.hpp"

namespace IronFrost {
  struct MeshData;
  struct ImageData;
  struct FontData; 
  struct ModelData;

  class IResourceManager {
    protected:
      std::unordered_map<StringID, MeshHandle> m_meshHandles;
      std::unordered_map<StringID, TextureHandle> m_textureHandles;
      std::unordered_map<StringID, ModelHandle> m_modelHandles;
      std::unordered_map<StringID, FontHandle> m_fontHandles;
      std::unordered_map<StringID, ShaderHandle> m_shaderHandles;

      MaterialLibrary m_materialLibrary;

      void registerListeners() {
        EventDispatcher& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

        eventDispatcher.registerListener<LoadMeshEvent>([&](const LoadMeshEvent& event) {
          createMesh(event.name(), event.meshData());
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadModelEvent>([&](const LoadModelEvent& event) {
          createModel(event.name(), event.modelData());
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadShaderEvent>([&](const LoadShaderEvent& event) {
          createShader(event.name(), event.shaderData().vertexShader, event.shaderData().fragmentShader);
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadTextureEvent>([&](const LoadTextureEvent& event) {
          createTexture(event.name(), event.imageData());
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadFontEvent>([&](const LoadFontEvent& event) {
          createFont(event.name(), event.fontData());
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadMaterialEvent>([&](const LoadMaterialEvent& event) {
          createMaterial(event.name(), event.materialData());
          event.callCallback();
        });
      }

    public:
      IResourceManager() {
        registerListeners();
      };
      
      virtual ~IResourceManager() = default;

      virtual MeshHandle createMesh(const MeshData& meshData) = 0;
      virtual const MeshHandle& createMesh(const StringID& name, const MeshData& meshData) = 0;
      virtual void destroyMesh(const StringID& name) = 0;

      virtual TextureHandle createTexture(const ImageData& imageData) = 0;
      virtual const TextureHandle& createTexture(const StringID& name, const ImageData& imageData) = 0;
      virtual TextureHandle createTextureArray(const std::vector<std::unique_ptr<ImageData>>& imageDataArray) = 0;
      virtual const TextureHandle& createTextureArray(const StringID& name, const std::vector<std::unique_ptr<ImageData>>& imageDataArray) = 0;
      virtual void destroyTexture(const StringID& name) = 0;

      virtual const ModelHandle& createModel(const StringID& name, const ModelData& modelData) = 0;
      virtual void destroyModel(const StringID& name) = 0;

      virtual const FontHandle& createFont(const StringID& name, const FontData& fontData) = 0;
      virtual void destroyFont(const StringID& name) = 0;

      virtual const ShaderHandle& createShader(const StringID& name, const std::string& vertexShaderSource, const std::string& fragmentShaderSource) = 0;
      virtual void destroyShader(const StringID& name) = 0;

      const MeshHandle& getMesh(const StringID& name) const {
        auto it = m_meshHandles.find(name);
        if (it != m_meshHandles.end()) {
          return it->second;
        }
    
        std::cerr << "Mesh not found: " + StringID::getString(name) << std::endl;
        return m_meshHandles.at(FallbackResources::FALLBACK_MESH_NAME);
      }

      const TextureHandle& getTexture(const StringID& name) const {
        auto it = m_textureHandles.find(name);
        if (it != m_textureHandles.end()) {
          return it->second;
        }
    
        std::cerr << "Texture not found: " + StringID::getString(name) << std::endl;
        return m_textureHandles.at(FallbackResources::FALLBACK_TEXTURE_NAME);
      }

      const ModelHandle& getModel(const StringID& name) const {
        auto it = m_modelHandles.find(name);
        if (it != m_modelHandles.end()) {
          return it->second;
        }

        std::cerr << "Model not found: " + StringID::getString(name) << std::endl;
        return m_modelHandles.at(FallbackResources::FALLBACK_MODEL_NAME);
      }

      const FontHandle& getFont(const StringID &name) const {
        auto it = m_fontHandles.find(name);
        if (it != m_fontHandles.end())
        {
          return it->second;
        }
    
        throw std::runtime_error("Font not found: " + StringID::getString(name));
      }

      const ShaderHandle& getShader(const StringID& name) const {
        auto it = m_shaderHandles.find(name);
        if (it != m_shaderHandles.end()) {
          return it->second;
        }
    
        throw std::runtime_error("Shader not found: " + StringID::getString(name));
      }

      const Material& createMaterial(const StringID& name, const MaterialData& materialData) {
        if (const BlinnPhongMaterialData* data = std::get_if<BlinnPhongMaterialData>(&materialData.data)) {
          BlinnPhongMaterial blinnPhongMaterial;
          
          blinnPhongMaterial.ambient = data->ambient;
          blinnPhongMaterial.diffuse = data->diffuse;
          blinnPhongMaterial.specular = data->specular;
          blinnPhongMaterial.shininess = data->shininess;

          if (data->textureName != StringID("")) {
            blinnPhongMaterial.baseColor = getTexture(data->textureName);
          }

          addMaterial(name, blinnPhongMaterial);
        } else if (const PBRMaterialData* data = std::get_if<PBRMaterialData>(&materialData.data)) {
          PBRMaterial pbrMaterial;
          pbrMaterial.pbrTextureArray = createTextureArray(data->textureArray);

          addMaterial(name, pbrMaterial);
        }

        return m_materialLibrary.getMaterial(name);
      }

      void addMaterial(const StringID& name, const Material& material) {
        m_materialLibrary.addMaterial(name, material);
      }

      const Material& getMaterial(const StringID& name) const {
        return m_materialLibrary.getMaterial(name);
      }

      void destroyResource(const StringID& name, ResourceType type) {
        switch (type) {
          case ResourceType::MESH:
            return destroyMesh(name);
          case ResourceType::TEXTURE:
            return destroyTexture(name);
          case ResourceType::MODEL:
            return destroyModel(name);
          case ResourceType::FONT:
            return destroyFont(name);
          case ResourceType::SHADER:
            return destroyShader(name);
          default:
            return;
        }
      }
  };
}

#endif
