#ifndef __IF__MATERIAL_LIBRARY_HPP
#define __IF__MATERIAL_LIBRARY_HPP

// C++ standard library
#include <unordered_map>

// Local includes
#include "../utils/string_id.hpp"
#include "fallback_resources.hpp"

namespace IronFrost {
  class MaterialLibrary {
    private:
      std::unordered_map<StringID, Material> m_materials;

    public:
      MaterialLibrary() {
        m_materials = {
          { FallbackResources::FALLBACK_MATERIAL_NAME, BlinnPhongMaterial{} },
          { StringID("material::emerald"), BlinnPhongMaterial{{0.0215f, 0.1745f, 0.0215f}, {0.07568f, 0.61424f, 0.07568f}, {0.633f, 0.727811f, 0.633f}, 0.6f * 128.0f}},
          { StringID("material::jade"), BlinnPhongMaterial{{0.135f, 0.2225f, 0.1575f}, {0.54f, 0.89f, 0.63f}, {0.316228f, 0.316228f, 0.316228f}, 0.1f * 128.0f}},
          { StringID("material::obsidian"), <PERSON><PERSON>n<PERSON>hongMaterial{{0.05375f, 0.05f, 0.06625f}, {0.18275f, 0.17f, 0.22525f}, {0.332741f, 0.328634f, 0.346435f}, 0.3f * 128.0f}},
          { StringID("material::pearl"), BlinnPhongMaterial{{0.25f, 0.20725f, 0.20725f}, {1.0f, 0.829f, 0.829f}, {0.296648f, 0.296648f, 0.296648f}, 0.088f * 128.0f}},
          { StringID("material::ruby"), BlinnPhongMaterial{{0.1745f, 0.01175f, 0.01175f}, {0.61424f, 0.04136f, 0.04136f}, {0.727811f, 0.626959f, 0.626959f}, 0.6f * 128.0f}},
          { StringID("material::turquoise"), BlinnPhongMaterial{{0.1f, 0.18725f, 0.1745f}, {0.396f, 0.74151f, 0.69102f}, {0.297254f, 0.30829f, 0.306678f}, 0.1f * 128.0f}},
          { StringID("material::brass"), BlinnPhongMaterial{{0.329412f, 0.223529f, 0.027451f}, {0.780392f, 0.568627f, 0.113725f}, {0.992157f, 0.941176f, 0.807843f}, 0.21794872f * 128.0f}},
          { StringID("material::bronze"), BlinnPhongMaterial{{0.2125f, 0.1275f, 0.054f}, {0.714f, 0.4284f, 0.18144f}, {0.393548f, 0.271906f, 0.166721f}, 0.2f * 128.0f}},
          { StringID("material::chrome"), BlinnPhongMaterial{{0.25f, 0.25f, 0.25f}, {0.4f, 0.4f, 0.4f}, {0.774597f, 0.774597f, 0.774597f}, 0.6f * 128.0f}},
          { StringID("material::gold"), BlinnPhongMaterial{{0.24725f, 0.1995f, 0.0745f}, {0.75164f, 0.60648f, 0.22648f}, {0.628281f, 0.555802f, 0.366065f}, 0.4f * 128.0f}},
          { StringID("material::silver"), BlinnPhongMaterial{{0.19225f, 0.19225f, 0.19225f}, {0.50754f, 0.50754f, 0.50754f}, {0.508273f, 0.508273f, 0.508273f}, 0.4f * 128.0f}}
        };
      }

      const Material& getMaterial(const StringID& name) const {
        auto it = m_materials.find(name);
        if (it != m_materials.end()) {
            return it->second;
        }

        return m_materials.at(FallbackResources::FALLBACK_MATERIAL_NAME);
      }

      void addMaterial(const StringID& name, const Material& material) {
        m_materials[name] = material;
      }
  };
}

#endif
