#ifndef __IF__GPU_HANDLES_HPP
#define __IF__GPU_HANDLES_HPP

// C standard library
#include <cstddef>

// C++ standard library
#include <functional>
#include <string>
#include <tuple>
#include <unordered_map>
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../utils/collision_math.hpp"

namespace IronFrost {
  struct MeshHandle {
    unsigned int VAO{0};
    unsigned int numIndices{0};
    unsigned int numVertices{0};

    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};

    bool operator==(const MeshHandle &other) const {
      return VAO == other.VAO;
    }
  };
  
  enum class TextureType {
    DIFFUSE,
    ATLAS,
    ARRAY
  };

  struct TextureHandle {
    unsigned int textureID{0};
    TextureType type{TextureType::DIFFUSE};

    bool operator==(const TextureHandle &other) const {
      return textureID == other.textureID;
    }
  };

  struct ModelHandle {
    struct Node {
      glm::mat4 transform{1.0f};

      std::vector<MeshHandle> meshes;
      std::vector<TextureHandle> textures;
      std::vector<Node> children;
    };

    Node rootNode;

    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};
  };

  struct GlyphHandle {
    unsigned int textureID{0};
    glm::vec2 size{0.0f, 0.0f};
    glm::vec2 bearing{0.0f, 0.0f};
    unsigned int advance{0};
  };

  struct FontHandle {
    unsigned int lineHeight{0};
    std::vector<GlyphHandle> glyphs{128};
  };

  struct ShaderHandle {
    unsigned int programID{0};
    std::unordered_map<std::string, std::tuple<int, unsigned int>> activeUniforms;

    std::tuple<int, unsigned int> getUniformLocation(const std::string &name) const {
      auto it = activeUniforms.find(name);
      if (it != activeUniforms.end()) {
        return it->second;
      }

      return std::make_tuple(-1, 0);
    }

    bool operator==(const ShaderHandle& other) const {
      return programID == other.programID;
    }
  };

  struct FramebufferHandle {
    unsigned int framebufferID{0};
    unsigned int textureID{0};

    bool operator==(const FramebufferHandle &other) const {
      return framebufferID == other.framebufferID;
    }
  };
}

namespace std {
  template <>
  struct hash<IronFrost::ShaderHandle> {
    std::size_t operator()(IronFrost::ShaderHandle const &shaderHandle) const noexcept {
      return std::hash<unsigned int>{}(shaderHandle.programID);
    }
  };
}

#endif
