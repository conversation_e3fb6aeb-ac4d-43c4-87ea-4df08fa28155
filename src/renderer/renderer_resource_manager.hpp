#ifndef __IF__RENDERER_RESOURCE_MANAGER_HPP
#define __IF__RENDERER_RESOURCE_MANAGER_HPP

// C++ standard library
#include <functional>

// Local includes
#include "../assets/asset_data_types.hpp"
#include "../events/event_dispatcher.hpp"
#include "../utils/string_id.hpp"

namespace IronFrost {
  // Forward declarations
  class AssetsManager;
  class AssetsLoader;
  class IRenderer;
  class IResourceManager;

  /**
   * RendererResourceManager handles loading and management of GPU-bound resources
   * such as meshes, textures, shaders, models, and materials.
   */
  class RendererResourceManager {
    private:
      AssetsManager& m_assetsManager;
      IRenderer& m_renderer;
      IResourceManager& m_resourceManager;
      EventDispatcher& m_eventDispatcher;

      // Individual resource loading methods
      void loadMeshes(const AssetsLoader& assetsLoader);
      void loadModels(const AssetsLoader& assetsLoader);
      void loadShaders(const AssetsLoader& assetsLoader);
      void loadTextures(const AssetsLoader& assetsLoader);
      void loadFonts(const AssetsLoader& assetsLoader);
      void loadMaterials(const AssetsLoader& assetsLoader);
      void loadPostprocessEffects(const AssetsLoader& assetsLoader);

    public:
      RendererResourceManager(AssetsManager& assetsManager, IRenderer& renderer, EventDispatcher& eventDispatcher);
      ~RendererResourceManager() = default;

      // Main resource loading interface
      void loadAllResources(const AssetsLoader& assetsLoader);
      void loadEssentialResources();
      void unloadAllResources();

      // Individual resource type loading (public for selective loading)
      void loadMeshesOnly(const AssetsLoader& assetsLoader) { loadMeshes(assetsLoader); }
      void loadTexturesOnly(const AssetsLoader& assetsLoader) { loadTextures(assetsLoader); }
      void loadShadersOnly(const AssetsLoader& assetsLoader) { loadShaders(assetsLoader); }
      void loadModelsOnly(const AssetsLoader& assetsLoader) { loadModels(assetsLoader); }
      void loadFontsOnly(const AssetsLoader& assetsLoader) { loadFonts(assetsLoader); }
      void loadMaterialsOnly(const AssetsLoader& assetsLoader) { loadMaterials(assetsLoader); }
      void loadPostprocessEffectsOnly(const AssetsLoader& assetsLoader) { loadPostprocessEffects(assetsLoader); }
  };
}

#endif
