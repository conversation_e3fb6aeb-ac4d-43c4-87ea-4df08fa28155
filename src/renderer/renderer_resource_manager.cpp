#include "renderer_resource_manager.hpp"

// C++ standard library
#include <iostream>

// Local includes
#include "../assets/assets.hpp"
#include "../assets/assets_loader.hpp"
#include "../assets/asset_primitive_types.hpp"
#include "fallback_resources.hpp"
#include "renderer.hpp"
#include "resource_events.hpp"
#include "resource_manager.hpp"

namespace IronFrost {
  RendererResourceManager::RendererResourceManager(AssetsManager& assetsManager, IRenderer& renderer, EventDispatcher& eventDispatcher)
    : m_assetsManager(assetsManager), m_renderer(renderer), m_resourceManager(renderer.getResourceManager()), m_eventDispatcher(eventDispatcher) {}

  void RendererResourceManager::loadAllResources(const AssetsLoader& assetsLoader) {
    std::cout << "Loading all renderer resources..." << std::endl;

    loadMeshes(assetsLoader);
    loadModels(assetsLoader);
    loadShaders(assetsLoader);
    loadTextures(assetsLoader);
    loadFonts(assetsLoader);
    loadMaterials(assetsLoader);
    loadPostprocessEffects(assetsLoader);

    std::cout << "All renderer resources loaded" << std::endl;
  }

  void RendererResourceManager::loadEssentialResources() {
    std::cout << "Loading essential renderer resources..." << std::endl;

    std::unique_ptr<MeshData> quadMeshData = m_assetsManager.createPrimitive(PrimitiveType::QUAD);
    m_resourceManager.createMesh(FallbackResources::DEFAULT_QUAD_NAME, *quadMeshData);

    std::unique_ptr<MeshData> fallbackMeshData = m_assetsManager.createPrimitive(PrimitiveType::CUBE);
    m_resourceManager.createMesh(FallbackResources::FALLBACK_MESH_NAME, *fallbackMeshData);
    m_resourceManager.createMesh(FallbackResources::DEBUG_CUBE_NAME, *fallbackMeshData);
    std::unique_ptr<ImageData> fallbackImageData = m_assetsManager.loadImage("config/1x1-ffffffff.png");
    m_resourceManager.createTexture(FallbackResources::FALLBACK_TEXTURE_NAME, *fallbackImageData);

    ModelData fallbackModelData;
    fallbackModelData.rootNode.meshes.emplace_back(std::move(fallbackMeshData));
    fallbackModelData.rootNode.textures.emplace_back(std::move(fallbackImageData));
    m_resourceManager.createModel(FallbackResources::FALLBACK_MODEL_NAME, fallbackModelData);

    std::cout << "Essential renderer resources loaded" << std::endl;
  }

  void RendererResourceManager::unloadAllResources() {
    std::cout << "Unloading all renderer resources..." << std::endl;
    // Resource manager handles cleanup automatically
    std::cout << "Renderer resources unloaded" << std::endl;
  }

  void RendererResourceManager::loadMeshes(const AssetsLoader& assetsLoader) {
    assetsLoader.loadMeshes(m_assetsManager,
      [&](const StringID _name, const MeshData& _meshData) {
        m_eventDispatcher.dispatch<LoadMeshEvent>(_name, _meshData, 
          [&_name, &_eventDispatcher = m_eventDispatcher, &_assetsManager = m_assetsManager]() {
            _assetsManager.unloadMesh(_name);
            _eventDispatcher.dispatchAsync<ResourceCreatedEvent>(_name, ResourceType::MESH);
          }
        );
      });
  }

  void RendererResourceManager::loadModels(const AssetsLoader& assetsLoader) {
    assetsLoader.loadModels(m_assetsManager,
      [&](const StringID _name, const ModelData& _modelData) {
        m_eventDispatcher.dispatch<LoadModelEvent>(_name, _modelData, 
          [&_name, &_eventDispatcher = m_eventDispatcher, &_assetsManager = m_assetsManager]() {
            _assetsManager.unloadModel(_name);
            _eventDispatcher.dispatchAsync<ResourceCreatedEvent>(_name, ResourceType::MODEL);
          }
        );
      });
  }

  void RendererResourceManager::loadShaders(const AssetsLoader& assetsLoader) {
    assetsLoader.loadShaders(m_assetsManager,
      [&](const StringID _name, const ShaderData& _shaderData) {
        m_eventDispatcher.dispatch<LoadShaderEvent>(_name, _shaderData, 
          [&_name, &_eventDispatcher = m_eventDispatcher, &_assetsManager = m_assetsManager]() {
            _assetsManager.unloadShader(_name);
            _eventDispatcher.dispatchAsync<ResourceCreatedEvent>(_name, ResourceType::SHADER);
          }
        );
      });
  }

  void RendererResourceManager::loadTextures(const AssetsLoader& assetsLoader) {
    assetsLoader.loadTextures(m_assetsManager,
      [&](const StringID _name, const ImageData& _imageData) {
        m_eventDispatcher.dispatch<LoadTextureEvent>(_name, _imageData, 
          [&_name, &_eventDispatcher = m_eventDispatcher, &_assetsManager = m_assetsManager]() {
            _assetsManager.unloadImage(_name);
            _eventDispatcher.dispatchAsync<ResourceCreatedEvent>(_name, ResourceType::TEXTURE);
          }
        );
      });
  }

  void RendererResourceManager::loadFonts(const AssetsLoader& assetsLoader) {
    assetsLoader.loadFonts(m_assetsManager,
      [&](const StringID _name, const FontData& _fontData) {
        m_eventDispatcher.dispatch<LoadFontEvent>(_name, _fontData, 
          [&_name, &_eventDispatcher = m_eventDispatcher, &_assetsManager = m_assetsManager]() {
            _assetsManager.unloadFont(_name);
            _eventDispatcher.dispatchAsync<ResourceCreatedEvent>(_name, ResourceType::FONT);
          }
        );
      });
  }

  void RendererResourceManager::loadMaterials(const AssetsLoader& assetsLoader) {
    assetsLoader.loadMaterials(m_assetsManager,
      [&](const StringID _name, const MaterialData& _materialData) {
        m_eventDispatcher.dispatch<LoadMaterialEvent>(_name, _materialData, 
          [&_name, &_eventDispatcher = m_eventDispatcher]() {
            _eventDispatcher.dispatchAsync<ResourceCreatedEvent>(_name, ResourceType::MATERIAL);
          }
        );
      });
  }

  void RendererResourceManager::loadPostprocessEffects(const AssetsLoader& assetsLoader) {
    assetsLoader.loadPostprocessEffects(
      [&](const StringID _name, const std::vector<std::string>& _shaderNames) {
        // Call renderer directly like the old ResourceLoader did
        m_renderer.createPostprocessEffect(_name, _shaderNames);
        m_eventDispatcher.dispatchAsync<ResourceCreatedEvent>(_name, ResourceType::POSTPROCESS_EFFECT);
      });
  }
}
