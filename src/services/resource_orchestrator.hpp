#ifndef __IF__RESOURCE_ORCHESTRATOR_HPP
#define __IF__RESOURCE_ORCHESTRATOR_HPP

// C++ standard library
#include <memory>

// Local includes
#include "../events/event_dispatcher.hpp"
#include "service.hpp"

namespace IronFrost {
  // Forward declarations
  class AssetsManager;
  class AssetsLoader;
  class IRenderer;
  class IAudioEngine;
  class RendererResourceManager;
  class AudioResourceManager;

  /**
   * ResourceOrchestrator coordinates resource loading across different subsystems.
   * It acts as a service that manages specialized resource managers for different
   * types of resources (renderer, audio, etc.).
   */
  class ResourceOrchestrator : public IService {
    private:
      AssetsManager& m_assetsManager;
      EventDispatcher& m_eventDispatcher;
      
      // Specialized resource managers
      std::unique_ptr<RendererResourceManager> m_rendererResources;
      std::unique_ptr<AudioResourceManager> m_audioResources;
      
      bool m_initialized;

    public:
      explicit ResourceOrchestrator(AssetsManager& assetsManager, EventDispatcher& eventDispatcher);
      ~ResourceOrchestrator(); // Implemented in .cpp to handle incomplete types

      // Initialization
      void initialize(IRenderer& renderer, IAudioEngine& audioEngine);

      // Resource loading coordination
      void loadResources(const AssetsLoader& assetsLoader);
      void loadEssentialResources();
      void unloadAllResources();
  };
}

#endif
