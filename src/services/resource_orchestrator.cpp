#include "resource_orchestrator.hpp"

// C++ standard library
#include <iostream>
#include <stdexcept>

// Local includes
#include "../assets/assets.hpp"
#include "../assets/assets_loader.hpp"
#include "../audio/audio_resource_manager.hpp"
#include "../renderer/renderer_resource_manager.hpp"

namespace IronFrost {
  ResourceOrchestrator::ResourceOrchestrator(AssetsManager& assetsManager, EventDispatcher& eventDispatcher)
    : m_assetsManager(assetsManager), m_eventDispatcher(eventDispatcher), m_initialized(false) {}

  ResourceOrchestrator::~ResourceOrchestrator() {
    unloadAllResources();
  }

  void ResourceOrchestrator::initialize(IRenderer& renderer, IAudioEngine& audioEngine) {
    if (m_initialized) {
      std::cout << "ResourceOrchestrator already initialized" << std::endl;
      return;
    }

    std::cout << "Initializing ResourceOrchestrator..." << std::endl;

    m_rendererResources = std::make_unique<RendererResourceManager>(m_assetsManager, renderer, m_eventDispatcher);
    m_audioResources = std::make_unique<AudioResourceManager>(m_assetsManager, audioEngine, m_eventDispatcher);

    m_initialized = true;
    std::cout << "ResourceOrchestrator initialized successfully" << std::endl;
  }

  void ResourceOrchestrator::loadResources(const AssetsLoader& assetsLoader) {
    if (!m_initialized) {
      throw std::runtime_error("ResourceOrchestrator not initialized. Call initialize() first.");
    }

    std::cout << "Loading resources through ResourceOrchestrator..." << std::endl;

    std::cout << "Loading renderer resources..." << std::endl;
    m_rendererResources->loadAllResources(assetsLoader);

    std::cout << "Loading audio resources..." << std::endl;
    m_audioResources->loadAllAudio(assetsLoader);

    std::cout << "All resources loaded successfully" << std::endl;
  }

  void ResourceOrchestrator::loadEssentialResources() {
    if (!m_initialized) {
      throw std::runtime_error("ResourceOrchestrator not initialized. Call initialize() first.");
    }

    std::cout << "Loading essential resources..." << std::endl;
    
    m_rendererResources->loadEssentialResources();
    
    std::cout << "Essential resources loaded" << std::endl;
  }

  void ResourceOrchestrator::unloadAllResources() {
    if (!m_initialized) {
      return;
    }

    std::cout << "Unloading all resources..." << std::endl;

    // Unload audio resources
    if (m_audioResources) {
      m_audioResources->unloadAllAudio();
    }

    // Unload renderer resources
    if (m_rendererResources) {
      m_rendererResources->unloadAllResources();
    }

    std::cout << "All resources unloaded" << std::endl;
  }
}
