#include "gui.hpp"

// C++ standard library
#include <exception>
#include <functional>
#include <memory>
#include <stdexcept>
#include <utility>

// Local includes
#include "../utils/game_context.hpp"

namespace IronFrost {
  void GUI::update(float deltaTime, GameContext& gameContext) {
    for(auto& [id, widget] : m_widgets) {
      widget->update(deltaTime, gameContext);
    }
  }

  void GUI::addWidget(const StringID& id, std::unique_ptr<Widget> widget) {
    m_widgets.emplace(id, std::move(widget));
  }

  void GUI::addWidgetRaw(const StringID& id, Widget* widget) {
    m_widgets.emplace(id, std::unique_ptr<Widget>(widget));
  }

  void GUI::removeWidget(const StringID& id) {
    m_widgets.erase(id);
  }

  Widget& GUI::getWidget(const StringID& id) const {
    auto it = m_widgets.find(id);
    if(it != m_widgets.end()) {
        return *it->second;
    }
    throw std::runtime_error("Widget not found");
  }

  void GUI::traverseWidgets(const std::function<void(Widget&)>& func) {
    for(auto& [id, widget] : m_widgets) {
      func(*widget);
    }
  }
}
