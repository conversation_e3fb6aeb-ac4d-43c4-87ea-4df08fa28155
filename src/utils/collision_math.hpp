#ifndef __IF__COLLISION_MATH_HPP
#define __IF__COLLISION_MATH_HPP

// Third-party libraries
#include <glm/glm.hpp>

// C++ standard library
#include <optional>

namespace IronFrost {
  namespace CollisionMath {
    /**
     * Represents a plane in 3D space using the equation: ax + by + cz + d = 0
     * The normal vector (a, b, c) should be normalized for distance calculations
     */
    struct Plane {
      glm::vec3 normal;  // Normal vector (should be normalized)
      float distance;    // Distance from origin along normal
      
      Plane(const glm::vec3& normal, float distance) 
        : normal(glm::normalize(normal)), distance(distance) {}
      
      // Create plane from point and normal
      Plane(const glm::vec3& point, const glm::vec3& normal) 
        : normal(glm::normalize(normal)) {
        this->distance = glm::dot(point, this->normal);
      }
      
      // Create plane from three points
      Plane(const glm::vec3& p1, const glm::vec3& p2, const glm::vec3& p3) {
        glm::vec3 v1 = p2 - p1;
        glm::vec3 v2 = p3 - p1;
        normal = glm::normalize(glm::cross(v1, v2));
        distance = glm::dot(p1, normal);
      }
    };
    
    /**
     * Represents a ray in 3D space
     */
    struct Ray {
      glm::vec3 origin;
      glm::vec3 direction;  // Should be normalized
      
      Ray(const glm::vec3& origin, const glm::vec3& direction) 
        : origin(origin), direction(glm::normalize(direction)) {}
    };
    
    /**
     * Axis-Aligned Bounding Box
     */
    struct AABB {
      glm::vec3 min;
      glm::vec3 max;
      
      AABB(const glm::vec3& min, const glm::vec3& max) : min(min), max(max) {}
      
      // Create AABB from center and size
      static AABB fromCenterAndSize(const glm::vec3& center, const glm::vec3& size) {
        glm::vec3 halfSize = size * 0.5f;
        return AABB(center - halfSize, center + halfSize);
      }
      
      glm::vec3 getCenter() const {
        return (min + max) * 0.5f;
      }
      
      glm::vec3 getSize() const {
        return max - min;
      }
    };
    
    /**
     * Calculate the signed distance from a point to a plane
     * Positive distance means the point is on the side of the plane the normal points to
     * Negative distance means the point is on the opposite side
     */
    inline float distanceToPlane(const glm::vec3& point, const Plane& plane) {
      return glm::dot(point, plane.normal) - plane.distance;
    }
    
    /**
     * Check if a point is above a plane (in the direction of the normal)
     */
    inline bool isPointAbovePlane(const glm::vec3& point, const Plane& plane) {
      return distanceToPlane(point, plane) > 0.0f;
    }
    
    /**
     * Check if a point is below a plane (opposite to the normal direction)
     */
    inline bool isPointBelowPlane(const glm::vec3& point, const Plane& plane) {
      return distanceToPlane(point, plane) < 0.0f;
    }
    
    /**
     * Project a point onto a plane
     */
    inline glm::vec3 projectPointOntoPlane(const glm::vec3& point, const Plane& plane) {
      float distance = distanceToPlane(point, plane);
      return point - distance * plane.normal;
    }
    
    /**
     * Ray-plane intersection
     * Returns the parameter t where intersection point = ray.origin + t * ray.direction
     * Returns nullopt if ray is parallel to plane or intersection is behind ray origin
     */
    inline std::optional<float> rayPlaneIntersection(const Ray& ray, const Plane& plane) {
      float denominator = glm::dot(ray.direction, plane.normal);
      
      // Ray is parallel to plane
      if (std::abs(denominator) < 1e-6f) {
        return std::nullopt;
      }
      
      float t = (plane.distance - glm::dot(ray.origin, plane.normal)) / denominator;
      
      // Intersection is behind ray origin
      if (t < 0.0f) {
        return std::nullopt;
      }
      
      return t;
    }
    
    /**
     * Check if a point is inside an AABB
     */
    inline bool isPointInAABB(const glm::vec3& point, const AABB& aabb) {
      return point.x >= aabb.min.x && point.x <= aabb.max.x &&
             point.y >= aabb.min.y && point.y <= aabb.max.y &&
             point.z >= aabb.min.z && point.z <= aabb.max.z;
    }
    
    /**
     * Check if two AABBs intersect
     */
    inline bool aabbIntersection(const AABB& a, const AABB& b) {
      return a.min.x <= b.max.x && a.max.x >= b.min.x &&
             a.min.y <= b.max.y && a.max.y >= b.min.y &&
             a.min.z <= b.max.z && a.max.z >= b.min.z;
    }
    
    /**
     * Get the closest point on an AABB to a given point
     */
    inline glm::vec3 closestPointOnAABB(const glm::vec3& point, const AABB& aabb) {
      return glm::vec3(
        glm::clamp(point.x, aabb.min.x, aabb.max.x),
        glm::clamp(point.y, aabb.min.y, aabb.max.y),
        glm::clamp(point.z, aabb.min.z, aabb.max.z)
      );
    }
    
    /**
     * Calculate the distance from a point to an AABB
     * Returns 0 if the point is inside the AABB
     */
    inline float distanceToAABB(const glm::vec3& point, const AABB& aabb) {
      glm::vec3 closestPoint = closestPointOnAABB(point, aabb);
      return glm::length(point - closestPoint);
    }

    inline AABB transformAABB(const AABB& aabb, const glm::mat4& transform) {
      glm::vec3 corners[8] = {
        {aabb.min.x, aabb.min.y, aabb.min.z},
        {aabb.max.x, aabb.min.y, aabb.min.z},
        {aabb.min.x, aabb.max.y, aabb.min.z},
        {aabb.max.x, aabb.max.y, aabb.min.z},
        {aabb.min.x, aabb.min.y, aabb.max.z},
        {aabb.max.x, aabb.min.y, aabb.max.z},
        {aabb.min.x, aabb.max.y, aabb.max.z},
        {aabb.max.x, aabb.max.y, aabb.max.z},
      };

      glm::vec3 min(FLT_MAX);
      glm::vec3 max(-FLT_MAX);

      for (const auto& corner : corners) {
        glm::vec3 transformed = glm::vec3(transform * glm::vec4(corner, 1.0f));
        min = glm::min(min, transformed);
        max = glm::max(max, transformed);
      }

      return AABB(min, max);
    }

    /**
     * Represents a sphere in 3D space
     */
    struct Sphere {
      glm::vec3 center;
      float radius;

      Sphere(const glm::vec3& center, float radius) : center(center), radius(radius) {}
    };

    /**
     * Check if a sphere intersects with a plane
     * Returns true if the sphere intersects or is below the plane
     */
    inline bool spherePlaneIntersection(const Sphere& sphere, const Plane& plane) {
      float distance = distanceToPlane(sphere.center, plane);
      return distance <= sphere.radius;
    }

    /**
     * Check if a sphere intersects with an AABB
     */
    inline bool sphereAABBIntersection(const Sphere& sphere, const AABB& aabb) {
      float distance = distanceToAABB(sphere.center, aabb);
      return distance <= sphere.radius;
    }

    /**
     * Get the penetration depth of a sphere into a plane
     * Returns positive value if sphere is penetrating the plane
     */
    inline float spherePlanePenetration(const Sphere& sphere, const Plane& plane) {
      float distance = distanceToPlane(sphere.center, plane);
      if (distance < sphere.radius) {
        return sphere.radius - distance;
      }
      return 0.0f;
    }

    /**
     * Resolve sphere-plane collision by moving the sphere out of the plane
     * Returns the corrected sphere center position
     */
    inline glm::vec3 resolveSpherePlaneCollision(const Sphere& sphere, const Plane& plane) {
      float penetration = spherePlanePenetration(sphere, plane);
      if (penetration > 0.0f) {
        return sphere.center + plane.normal * penetration;
      }
      return sphere.center;
    }

  } // namespace CollisionMath
} // namespace IronFrost

#endif
