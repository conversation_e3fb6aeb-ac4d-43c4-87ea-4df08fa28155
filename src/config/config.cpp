#include "config.hpp"

// C++ standard library
#include <iostream>
#include <string>
#include <vector>

// Local includes
#include "../assets/assets.hpp"
#include "../assets/assets_loader.hpp"
#include "../assets/assets_manifest.hpp"
#include "../audio/audio_engine.hpp"
#include "../events/event_dispatcher.hpp"
#include "../renderer/fallback_resources.hpp"
#include "../renderer/postprocess_effect.hpp"
#include "../renderer/renderer.hpp"
#include "../renderer/resource_manager.hpp"
#include "../services/resource_orchestrator.hpp"
#include "../services/service_locator.hpp"
#include "../vfs/vfs.hpp"

namespace IronFrost {
  Config::Config(IVFS &vfs) : m_vfs(vfs) {
    m_vfs.mount("data/config", "config");
    m_globalConfig = json::parse(m_vfs.readFile("config/global.json"));

    auto mounts = m_globalConfig["vfs"]["mount"];

    for (json::iterator it = mounts.begin(); it != mounts.end(); ++it) {
      auto mount = it.value();
      m_vfs.mount(mount["path"], mount["mountPoint"]);
    }
  }

  void Config::loadGlobalAssets(IRenderer& renderer, AssetsManager& assetsManager, IAudioEngine& audioEngine) {
    std::cout << "Loading global assets" << '\n';
    auto manifest = AssetsManifest::fromFile(m_vfs, "global_assets.json");
    auto assetsLoader = AssetsLoader::fromFile(m_vfs, "global_assets.json");

    ResourceOrchestrator& resourceOrchestrator = ServiceLocator::getService<ResourceOrchestrator>();
    resourceOrchestrator.initialize(renderer, audioEngine);
    resourceOrchestrator.loadEssentialResources();
    resourceOrchestrator.loadResources(*assetsLoader);

    std::cout << "Global assets loaded" << '\n';
  }

  std::vector<std::string> Config::getApplicationInitScripts() const {
    std::vector<std::string> scripts;

    if (m_globalConfig.contains("scripts") &&
        m_globalConfig["scripts"].contains("application") &&
        m_globalConfig["scripts"]["application"].contains("init")) {

      for (const auto& script : m_globalConfig["scripts"]["application"]["init"]) {
        scripts.push_back(script.get<std::string>());
      }
    }

    return scripts;
  }

  std::vector<std::string> Config::getApplicationPerFrameScripts() const {
    std::vector<std::string> scripts;

    if (m_globalConfig.contains("scripts") &&
        m_globalConfig["scripts"].contains("application") &&
        m_globalConfig["scripts"]["application"].contains("perFrame")) {

      for (const auto& script : m_globalConfig["scripts"]["application"]["perFrame"]) {
        scripts.push_back(script.get<std::string>());
      }
    }

    return scripts;
  }

  std::vector<std::string> Config::getApplicationInitScriptContents() const {
    std::vector<std::string> scriptContents;
    auto scriptPaths = getApplicationInitScripts();

    for (const auto& scriptPath : scriptPaths) {
      try {
        std::string scriptContent = m_vfs.readFile(scriptPath);
        scriptContents.push_back(scriptContent);
      } catch (const std::exception& e) {
        std::cout << "Error loading init script " << scriptPath << ": " << e.what() << '\n';
      }
    }

    return scriptContents;
  }

  std::vector<std::string> Config::getApplicationPerFrameScriptContents() const {
    std::vector<std::string> scriptContents;
    auto scriptPaths = getApplicationPerFrameScripts();

    for (const auto& scriptPath : scriptPaths) {
      try {
        std::string scriptContent = m_vfs.readFile(scriptPath);
        scriptContents.push_back(scriptContent);
      } catch (const std::exception& e) {
        std::cout << "Error loading per-frame script " << scriptPath << ": " << e.what() << '\n';
      }
    }

    return scriptContents;
  }
}
