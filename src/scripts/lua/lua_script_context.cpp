#include "lua_script_context.hpp"

// C++ standard library
#include <iostream>

// Local includes
#include "../../app/application.hpp"
#include "lua_api_binder.hpp"

namespace IronFrost {

  LuaScriptContext::LuaScriptContext(Application& application, LuaAPIBinder& apiBinder)
    : m_application(application), m_apiBinder(&apiBinder) {
  }

  void LuaScriptContext::initialize() {
    initializeLuaState();
    bindAPIs();
    setupContextVariables();
  }

  void LuaScriptContext::executeInitScripts(const std::vector<std::string>& scriptContents) {
    for (const auto& scriptContent : scriptContents) {
      try {
        m_luaState.script(scriptContent);
      } catch (const std::exception& e) {
        std::cout << "Error executing init script: " << e.what() << '\n';
      }
    }
  }

  void LuaScriptContext::registerPerFrameScripts(const std::vector<std::string>& scriptContents) {
    for (size_t i = 0; i < scriptContents.size(); ++i) {
      try {
        // Wrap script content in a function and compile it
        std::string functionName = "perFrameScript" + std::to_string(i);
        std::string wrappedScript = "function " + functionName + "()\n" + scriptContents[i] + "\nend";
        
        // Execute the function definition
        m_luaState.script(wrappedScript);
        
        // Get the compiled function
        sol::function compiledFunction = m_luaState[functionName];
        m_perFrameFunctions.push_back(compiledFunction);
      } catch (const std::exception& e) {
        std::cout << "Error compiling per-frame script: " << e.what() << '\n';
      }
    }
  }

  void LuaScriptContext::executePerFrame(float deltaTime) {
    m_luaState["deltaTime"] = deltaTime;

    for (const auto& scriptFunction : m_perFrameFunctions) {
      try {
        scriptFunction();
      } catch (const std::exception& e) {
        std::cout << "Error executing per-frame script function: " << e.what() << '\n';
      }
    }
  }

  void LuaScriptContext::initializeLuaState() {
    m_luaState.open_libraries(sol::lib::base, sol::lib::package, sol::lib::string, sol::lib::math, sol::lib::table, sol::lib::io);
  }

  void LuaScriptContext::bindAPIs() {
    // Bind all scripting APIs using the shared API binder
    m_apiBinder->bindAllAPIs(m_luaState);
    
    // Override print function with context-specific prefix
    std::string prefix = getPrintPrefix();
    m_apiBinder->bindPrintFunction(m_luaState, prefix);
  }

}
