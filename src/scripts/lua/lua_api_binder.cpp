#include "lua_api_binder.hpp"

// C++ standard library
#include <chrono>
#include <iostream>
#include <sstream>

// Local includes
#include "../../app/application.hpp"
#include "../../events/event_dispatcher.hpp"
#include "../../services/service_locator.hpp"
#include "../../utils/random.hpp"
#include "../../utils/string_id.hpp"
#include "../scheduled_task_manager.hpp"

// API includes
#include "api/audio_api.hpp"
#include "api/console_api.hpp"
#include "api/glm_api.hpp"
#include "api/gui_api.hpp"
#include "api/scene_api.hpp"
#include "api/standard_api.hpp"
#include "api/vfs_api.hpp"
#include "api/window_api.hpp"

namespace IronFrost {

  LuaAPIBinder::LuaAPIBinder(Application& application, ScheduledTaskManager& taskManager)
    : m_application(application), m_taskManager(taskManager) {
  }

  void LuaAPIBinder::bindAllAPIs(sol::state& luaState) {
    bindCoreAPIs(luaState);
    bindPrintFunction(luaState);
    bindVFSRequireFunction(luaState);
  }

  void LuaAPIBinder::bindPrintFunction(sol::state& luaState, const std::string& prefix) {
    luaState.set_function("print", 
      [prefix](sol::variadic_args args, sol::this_state state) {
        sol::state_view lua(state);
        std::ostringstream oss;
        
        if (!prefix.empty()) {
          oss << prefix << " ";
        }
        
        auto tostring = lua["tostring"];
        for (const auto& arg : args) {
          oss << tostring(arg).get<std::string>() << " ";
        }
        
        std::string message = oss.str();
        std::cout << message << '\n';
        
        try {
          ServiceLocator::getService<EventDispatcher>().dispatch<ConsoleLogEvent>(message);
        } catch (const std::exception& e) {}
      });
  }

  void LuaAPIBinder::bindVFSRequireFunction(sol::state& luaState) {
    IVFS& vfs = m_application.getVFS();

    luaState.set_function("require", [&vfs](const std::string& moduleName, sol::this_state state) -> sol::object {
      sol::state_view lua(state);
      std::string filePath = moduleName + ".lua";

      try {
        if (!vfs.exists(filePath)) {
          throw std::runtime_error("Module not found: " + moduleName + " (looked for: " + filePath + ")");
        }

        std::string scriptContent = vfs.readFile(filePath);
        sol::protected_function_result result = lua.script(scriptContent, sol::script_pass_on_error);

        if (!result.valid()) {
          sol::error err = result;
          throw std::runtime_error("Error loading module " + moduleName + ": " + err.what());
        }

        return result;
      } catch (const std::exception& e) {
        throw std::runtime_error("VFS require error for module " + moduleName + ": " + e.what());
      }
    });
  }

  void LuaAPIBinder::bindCoreAPIs(sol::state& luaState) {
    API::bindStandardAPI(luaState, m_taskManager);
    API::bindGLMAPI(luaState);
    API::bindVFSAPI(luaState, m_application.getVFS());
    API::bindWindowAPI(luaState, m_application.getWindow());
    API::bindConsoleAPI(luaState, m_application.getConsole());
    API::bindAudioAPI(luaState, m_application.getAudioEngine());
    API::bindGUIAPI(luaState);
    API::bindSceneAPI(luaState);
  }
}
