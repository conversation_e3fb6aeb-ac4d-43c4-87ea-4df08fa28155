-- Default scene initialization script
-- This script runs once when the default scene is loaded

print("Default scene init script loaded!")

-- Load modules using VFS-based require (only once per scene)
-- Shared modules (reusable across scenes)
CameraControls = require("scripts/shared/camera_controls")
GUIInteractions = require("scripts/shared/gui_interactions")

-- Scene-specific modules
DebugOutput = require("scripts/scenes/default/debug_output")

-- Initialize modules
CameraControls.init()
GUIInteractions.init()
DebugOutput.init()

-- Scene-specific variables (isolated from other scenes)
sceneStartTime = 0
sceneObjectCount = 5

print("Default scene initialized with " .. sceneObjectCount .. " objects")
