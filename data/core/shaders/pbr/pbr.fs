#version 330 core
out vec4 FragColor;

in vec2 TexCoord;
in vec3 Normal;
in vec3 FragPos;
in mat3 TBN;

uniform sampler2DArray pbrTextureArray;

struct AmbientLight {
    vec3 color;
    float intensity;
};
uniform AmbientLight ambientLight;

struct DirectionalLight {
    vec3 direction;
    vec3 color;
    float intensity;
};

#define MAX_DIRECTIONAL_LIGHTS 4
uniform int numDirectionalLights;
uniform DirectionalLight dirLights[MAX_DIRECTIONAL_LIGHTS];

struct PointLight {
    vec3 position;
    vec3 color;
    float intensity;
    float constant;
    float linear;
    float quadratic;
};

#define MAX_POINT_LIGHTS 8
uniform int numPointLights;
uniform PointLight pointLights[MAX_POINT_LIGHTS];

uniform vec3 cameraPosition;
uniform vec2 uvTiling;

vec3 calculateAmbientLight(vec3 albedo);
vec3 calculatePBRDirectionalLights(vec3 normal, vec3 viewDir, vec3 albedo, vec3 F0, float roughness, float metallic);
vec3 calculatePBRPointLights(vec3 normal, vec3 viewDir, vec3 FragPos, vec3 albedo, vec3 F0, float roughness, float metallic);
float calculateAttenuation(PointLight light, float distance);

vec3 fresnelSchlick(float cosTheta, vec3 F0);
float DistributionGGX(vec3 N, vec3 H, float roughness);
float GeometrySchlickGGX(float NdotV, float roughness);
float GeometrySmith(vec3 N, vec3 V, vec3 L, float roughness);

void main() {
    vec2 tiledUV = clamp(fract(TexCoord * uvTiling), 0.0, 1.0);
    vec3 tangentNormal = texture(pbrTextureArray, vec3(tiledUV, float(1))).rgb;
    tangentNormal = tangentNormal * 2.0 - 1.0; // Convert from [0,1] to [-1,1]
    vec3 normal = TBN * tangentNormal;

    vec4 textureColor = texture(pbrTextureArray, vec3(tiledUV, float(0)));

    if (textureColor.a == 0.0) {
        FragColor = vec4(1.0, 0.0, 0.0, 1.0);
        return;
    }

    vec3 albedo = textureColor.rgb;
    float roughness = texture(pbrTextureArray, vec3(tiledUV, float(3))).r;
    float metallic  = texture(pbrTextureArray, vec3(tiledUV, float(2))).r;

    vec3 F0 = vec3(0.04);
    F0 = mix(F0, albedo, metallic);

    vec3 ambientLight = calculateAmbientLight(albedo);
    vec3 directionalLights = calculatePBRDirectionalLights(normal, normalize(-FragPos), albedo, F0, roughness, metallic);
    vec3 pointLights = calculatePBRPointLights(normal, normalize(cameraPosition - FragPos), FragPos, albedo, F0, roughness, metallic);

    vec3 lighting = ambientLight + directionalLights + pointLights;

    FragColor = vec4(lighting, 1.0);

}

vec3 calculateAmbientLight(vec3 albedo) {
    return ambientLight.color * ambientLight.intensity * albedo;
}

vec3 calculatePBRDirectionalLights(vec3 normal, vec3 viewDir, vec3 albedo, vec3 F0, float roughness, float metallic) {
    vec3 totalDiffuseSpec = vec3(0.0);

    for (int i = 0; i < numDirectionalLights; i++) {
        vec3 L = normalize(-dirLights[i].direction);
        vec3 H = normalize(viewDir + L);

        float NDF = DistributionGGX(normal, H, roughness);
        float G = GeometrySmith(normal, viewDir, L, roughness);
        vec3 F = fresnelSchlick(max(dot(H, viewDir), 0.0), F0);

        float NdotV = max(dot(normal, viewDir), 0.0);
        float NdotL = max(dot(normal, L), 0.0);
        float denominator = 4.0 * NdotV * NdotL + 0.001;  
        vec3 specular = (NDF * G * F) / denominator;

        vec3 kS = F;
        vec3 kD = vec3(1.0) - kS;
        kD *= (1.0 - metallic);

        vec3 diffuse = kD * albedo / 3.14159265;

        totalDiffuseSpec += (diffuse + specular) * dirLights[i].color * dirLights[i].intensity * NdotL;
    }

    return totalDiffuseSpec;
}

vec3 calculatePBRPointLights(vec3 normal, vec3 viewDir, vec3 FragPos, vec3 albedo, vec3 F0, float roughness, float metallic) {
    vec3 totalDiffuseSpec = vec3(0.0);

    for (int i = 0; i < numPointLights; i++) {
        vec3 L = normalize(pointLights[i].position - FragPos);
        float distance = length(pointLights[i].position - FragPos);
        float attenuation = calculateAttenuation(pointLights[i], distance);

        vec3 H = normalize(viewDir + L);
        float NDF = DistributionGGX(normal, H, roughness);
        float G = GeometrySmith(normal, viewDir, L, roughness);
        vec3 F = fresnelSchlick(max(dot(H, viewDir), 0.0), F0);

        float NdotV = max(dot(normal, viewDir), 0.0);
        float NdotL = max(dot(normal, L), 0.0);
        float denominator = 4.0 * NdotV * NdotL + 0.001;  
        vec3 specular = (NDF * G * F) / denominator;

        vec3 kS = F;
        vec3 kD = vec3(1.0) - kS;
        kD *= (1.0 - metallic);

        vec3 diffuse = kD * albedo / 3.14159265;

        totalDiffuseSpec += (diffuse + specular) * pointLights[i].color * pointLights[i].intensity * attenuation * NdotL;
    }

    return totalDiffuseSpec;
}

float calculateAttenuation(PointLight light, float distance) {
    return 1.0 / (
        light.constant + 
        light.linear * distance + 
        light.quadratic * (distance * distance) + 0.0001
    );
}

vec3 fresnelSchlick(float cosTheta, vec3 F0) {
    float factor = (1.0 - cosTheta);
    float fresnelFactor = factor * factor * factor * factor * factor;
    return F0 + (1.0 - F0) * fresnelFactor;
}

float DistributionGGX(vec3 N, vec3 H, float roughness) {
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH * NdotH;

    float denominator = (NdotH2 * (a2 - 1.0) + 1.0);
    denominator = 3.14159265 * denominator * denominator;

    return a2 / denominator;
}

float GeometrySchlickGGX(float NdotV, float roughness) {
    float r = (roughness + 1.0);
    float k = (r * r) / 8.0; // K is tuned for direct lighting

    return NdotV / (NdotV * (1.0 - k) + k);
}

float GeometrySmith(vec3 N, vec3 V, vec3 L, float roughness) {
    float NdotV = max(dot(N, V), 0.0);
    float NdotL = max(dot(N, L), 0.0);

    float ggx1 = GeometrySchlickGGX(NdotV, roughness);
    float ggx2 = GeometrySchlickGGX(NdotL, roughness);

    return ggx1 * ggx2;
}
