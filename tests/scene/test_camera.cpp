#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp>

// C++ standard library
#include <cmath>

// Third-party libraries
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

// Local includes
#include "scene/camera.hpp"
#include "test_utils.hpp"

using namespace IronFrost;
using namespace Catch::Matchers;

TEST_CASE("Camera construction and initialization", "[scene][camera][construction]") {
    SECTION("Default constructor") {
        Camera camera;
        
        // Check default values
        REQUIRE(isVec3Equal(camera.getPosition(), glm::vec3(0.0f, 0.0f, 1.0f)));
        
        // Matrices should be valid (non-zero)
        const auto& viewMatrix = camera.getViewMatrix();
        const auto& projMatrix = camera.getProjectionMatrix();
        
        REQUIRE_FALSE(isMat4Equal(viewMatrix, glm::mat4(0.0f)));
        REQUIRE_FALSE(isMat4Equal(projMatrix, glm::mat4(0.0f)));
    }

    SECTION("Parameterized constructor") {
        glm::vec3 position(1.0f, 2.0f, 3.0f);
        glm::vec3 direction(0.0f, 0.0f, -1.0f);
        glm::vec3 up(0.0f, 1.0f, 0.0f);
        float fov = 60.0f;
        float aspectRatio = 16.0f / 9.0f;
        float nearPlane = 0.01f;
        float farPlane = 1000.0f;

        Camera camera(position, direction, up, fov, aspectRatio, nearPlane, farPlane);
        
        REQUIRE(isVec3Equal(camera.getPosition(), position));
        
        // Matrices should be valid
        const auto& viewMatrix = camera.getViewMatrix();
        const auto& projMatrix = camera.getProjectionMatrix();
        
        REQUIRE_FALSE(isMat4Equal(viewMatrix, glm::mat4(0.0f)));
        REQUIRE_FALSE(isMat4Equal(projMatrix, glm::mat4(0.0f)));
    }

    SECTION("Constructor with different parameters") {
        // Test various parameter combinations
        std::vector<std::tuple<glm::vec3, glm::vec3, glm::vec3, float, float, float, float>> testCases = {
            {{0.0f, 0.0f, 0.0f}, {1.0f, 0.0f, 0.0f}, {0.0f, 1.0f, 0.0f}, 45.0f, 1.0f, 0.1f, 100.0f},
            {{10.0f, 5.0f, -5.0f}, {-1.0f, 0.0f, 0.0f}, {0.0f, 1.0f, 0.0f}, 90.0f, 2.0f, 1.0f, 500.0f},
            {{-5.0f, 10.0f, 15.0f}, {0.0f, -1.0f, 0.0f}, {0.0f, 0.0f, 1.0f}, 30.0f, 0.5f, 0.01f, 50.0f}
        };

        for (const auto& [pos, dir, up, fov, aspect, near, far] : testCases) {
            Camera camera(pos, dir, up, fov, aspect, near, far);
            REQUIRE(isVec3Equal(camera.getPosition(), pos));
            
            // Should have valid matrices
            REQUIRE_FALSE(isMat4Equal(camera.getViewMatrix(), glm::mat4(0.0f)));
            REQUIRE_FALSE(isMat4Equal(camera.getProjectionMatrix(), glm::mat4(0.0f)));
        }
    }
}

TEST_CASE("Camera position and orientation", "[scene][camera][position]") {
    Camera camera;

    SECTION("Position setter and getter") {
        glm::vec3 newPosition(5.0f, 10.0f, -3.0f);
        camera.setPosition(newPosition);
        
        REQUIRE(isVec3Equal(camera.getPosition(), newPosition));
    }

    SECTION("Multiple position changes") {
        std::vector<glm::vec3> positions = {
            {0.0f, 0.0f, 0.0f},
            {1.0f, 2.0f, 3.0f},
            {-5.0f, 10.0f, -15.0f},
            {100.0f, -50.0f, 25.0f}
        };

        for (const auto& pos : positions) {
            camera.setPosition(pos);
            REQUIRE(isVec3Equal(camera.getPosition(), pos));
        }
    }

    SECTION("Direction setting") {
        glm::vec3 newDirection(1.0f, 0.0f, 0.0f);
        camera.setDirection(newDirection);
        
        // Direction should be normalized
        // We can't directly access direction, but we can test through movement
        glm::vec3 originalPos = camera.getPosition();
        camera.moveForward(1.0f, 1.0f);
        glm::vec3 newPos = camera.getPosition();
        
        glm::vec3 movement = newPos - originalPos;
        REQUIRE(isVec3Equal(glm::normalize(movement), glm::normalize(newDirection)));
    }

    SECTION("Up vector setting") {
        glm::vec3 newUp(1.0f, 0.0f, 0.0f);
        camera.setUp(newUp);
        
        // Test that the camera still functions correctly
        REQUIRE_NOTHROW(camera.moveForward(1.0f, 1.0f));
        REQUIRE_NOTHROW(camera.turnLeft(45.0f, 1.0f));
    }
}

TEST_CASE("Camera projection parameters", "[scene][camera][projection]") {
    Camera camera;

    SECTION("Field of view") {
        std::vector<float> fovValues = {30.0f, 45.0f, 60.0f, 90.0f, 120.0f};
        
        for (float fov : fovValues) {
            camera.setFov(fov);
            
            // Should update projection matrix
            const auto& projMatrix = camera.getProjectionMatrix();
            REQUIRE_FALSE(isMat4Equal(projMatrix, glm::mat4(0.0f)));
        }
    }

    SECTION("Aspect ratio") {
        std::vector<float> aspectRatios = {1.0f, 4.0f/3.0f, 16.0f/9.0f, 21.0f/9.0f, 0.5f};
        
        for (float aspect : aspectRatios) {
            camera.setAspectRatio(aspect);
            
            // Should update projection matrix
            const auto& projMatrix = camera.getProjectionMatrix();
            REQUIRE_FALSE(isMat4Equal(projMatrix, glm::mat4(0.0f)));
        }
    }

    SECTION("Near and far planes") {
        std::vector<std::pair<float, float>> planeValues = {
            {0.01f, 100.0f},
            {0.1f, 1000.0f},
            {1.0f, 500.0f},
            {0.001f, 10000.0f}
        };
        
        for (const auto& [near, far] : planeValues) {
            camera.setNearPlane(near);
            camera.setFarPlane(far);
            
            // Should update projection matrix
            const auto& projMatrix = camera.getProjectionMatrix();
            REQUIRE_FALSE(isMat4Equal(projMatrix, glm::mat4(0.0f)));
        }
    }

    SECTION("Projection matrix consistency") {
        float fov = 60.0f;
        float aspect = 16.0f / 9.0f;
        float near = 0.1f;
        float far = 100.0f;
        
        camera.setFov(fov);
        camera.setAspectRatio(aspect);
        camera.setNearPlane(near);
        camera.setFarPlane(far);
        
        // Compare with manually created projection matrix
        glm::mat4 expectedProj = glm::perspective(glm::radians(fov), aspect, near, far);
        REQUIRE(isMat4Equal(camera.getProjectionMatrix(), expectedProj));
    }
}

TEST_CASE("Camera movement", "[scene][camera][movement]") {
    Camera camera;

    SECTION("Forward movement") {
        glm::vec3 originalPos = camera.getPosition();
        float speed = 5.0f;
        float deltaTime = 0.1f;
        
        camera.moveForward(speed, deltaTime);
        
        glm::vec3 newPos = camera.getPosition();
        glm::vec3 movement = newPos - originalPos;
        
        // Should move in the negative Z direction (default forward)
        REQUIRE(movement.z < 0.0f);
        REQUIRE(isFloatEqual(glm::length(movement), speed * deltaTime));
    }

    SECTION("Backward movement") {
        glm::vec3 originalPos = camera.getPosition();
        float speed = 3.0f;
        float deltaTime = 0.2f;
        
        camera.moveBackward(speed, deltaTime);
        
        glm::vec3 newPos = camera.getPosition();
        glm::vec3 movement = newPos - originalPos;
        
        // Should move in the positive Z direction (opposite of forward)
        REQUIRE(movement.z > 0.0f);
        REQUIRE(isFloatEqual(glm::length(movement), speed * deltaTime));
    }

    SECTION("Left and right movement") {
        glm::vec3 originalPos = camera.getPosition();
        float speed = 2.0f;
        float deltaTime = 0.5f;
        
        // Move right
        camera.moveRight(speed, deltaTime);
        glm::vec3 rightPos = camera.getPosition();
        glm::vec3 rightMovement = rightPos - originalPos;
        
        // Should move in positive X direction (default right)
        REQUIRE(rightMovement.x > 0.0f);
        REQUIRE(isFloatEqual(glm::length(rightMovement), speed * deltaTime));
        
        // Reset position and move left
        camera.setPosition(originalPos);
        camera.moveLeft(speed, deltaTime);
        glm::vec3 leftPos = camera.getPosition();
        glm::vec3 leftMovement = leftPos - originalPos;
        
        // Should move in negative X direction (opposite of right)
        REQUIRE(leftMovement.x < 0.0f);
        REQUIRE(isFloatEqual(glm::length(leftMovement), speed * deltaTime));
    }

    SECTION("Movement with different speeds and delta times") {
        std::vector<std::pair<float, float>> speedTimeValues = {
            {1.0f, 1.0f},
            {10.0f, 0.1f},
            {0.5f, 2.0f},
            {100.0f, 0.01f}
        };
        
        for (const auto& [speed, deltaTime] : speedTimeValues) {
            glm::vec3 originalPos = camera.getPosition();
            camera.moveForward(speed, deltaTime);
            glm::vec3 newPos = camera.getPosition();
            
            float expectedDistance = speed * deltaTime;
            float actualDistance = glm::length(newPos - originalPos);
            
            REQUIRE(isFloatEqual(actualDistance, expectedDistance));
            
            // Reset for next test
            camera.setPosition(originalPos);
        }
    }
}

TEST_CASE("Camera rotation", "[scene][camera][rotation]") {
    Camera camera;

    SECTION("Left and right turning") {
        // Test turning left
        glm::vec3 originalPos = camera.getPosition();
        camera.turnLeft(90.0f, 1.0f); // 90 degrees in 1 second

        // Position should remain the same
        REQUIRE(isVec3Equal(camera.getPosition(), originalPos));

        // Test movement after turning - should move in different direction
        camera.moveForward(1.0f, 1.0f);
        glm::vec3 newPos = camera.getPosition();
        glm::vec3 movement = newPos - originalPos;

        // After turning left 90 degrees, forward should be roughly in -X direction
        REQUIRE(movement.x < 0.0f);
        REQUIRE(std::abs(movement.z) < 0.1f); // Should be minimal Z movement
    }

    SECTION("Up and down turning") {
        glm::vec3 originalPos = camera.getPosition();

        // Turn up
        camera.turnUp(45.0f, 1.0f);

        // Position should remain the same
        REQUIRE(isVec3Equal(camera.getPosition(), originalPos));

        // Test movement after turning up
        camera.moveForward(1.0f, 1.0f);
        glm::vec3 newPos = camera.getPosition();
        glm::vec3 movement = newPos - originalPos;

        // Should have positive Y component (moving up)
        REQUIRE(movement.y > 0.0f);
    }

    SECTION("Turn right and down") {
        glm::vec3 originalPos = camera.getPosition();

        // Turn right (should be opposite of left)
        camera.turnRight(90.0f, 1.0f);
        camera.moveForward(1.0f, 1.0f);
        glm::vec3 rightPos = camera.getPosition();

        // Reset and turn left for comparison
        camera.setPosition(originalPos);
        camera.setDirection(glm::vec3(0.0f, 0.0f, -1.0f)); // Reset direction
        camera.turnLeft(90.0f, 1.0f);
        camera.moveForward(1.0f, 1.0f);
        glm::vec3 leftPos = camera.getPosition();

        // Right and left turns should result in opposite X movements
        REQUIRE((rightPos.x - originalPos.x) * (leftPos.x - originalPos.x) < 0.0f);
    }

    SECTION("Rotation with different speeds and times") {
        std::vector<std::pair<float, float>> rotationValues = {
            {45.0f, 1.0f},
            {90.0f, 0.5f},
            {180.0f, 0.25f},
            {30.0f, 2.0f}
        };

        for (const auto& [speed, deltaTime] : rotationValues) {
            Camera testCamera;
            glm::vec3 originalPos = testCamera.getPosition();

            testCamera.turnLeft(speed, deltaTime);

            // Position should not change during rotation
            REQUIRE(isVec3Equal(testCamera.getPosition(), originalPos));

            // Should be able to move after rotation
            REQUIRE_NOTHROW(testCamera.moveForward(1.0f, 1.0f));
        }
    }

    SECTION("Gimbal lock prevention") {
        Camera testCamera;

        // Try to turn up beyond vertical limit
        for (int i = 0; i < 10; ++i) {
            testCamera.turnUp(45.0f, 1.0f); // Try to turn up 450 degrees total
        }

        // Should still be able to move and turn
        REQUIRE_NOTHROW(testCamera.moveForward(1.0f, 1.0f));
        REQUIRE_NOTHROW(testCamera.turnLeft(45.0f, 1.0f));
        REQUIRE_NOTHROW(testCamera.turnRight(45.0f, 1.0f));
    }
}

TEST_CASE("Camera matrix operations", "[scene][camera][matrices]") {
    Camera camera;

    SECTION("View matrix updates") {
        glm::mat4 originalView = camera.getViewMatrix();

        // Change position - should update view matrix
        camera.setPosition(glm::vec3(5.0f, 0.0f, 0.0f));
        glm::mat4 newView = camera.getViewMatrix();
        REQUIRE_FALSE(isMat4Equal(originalView, newView));

        // Change direction - should update view matrix
        originalView = newView;
        camera.setDirection(glm::vec3(1.0f, 0.0f, 0.0f));
        newView = camera.getViewMatrix();
        REQUIRE_FALSE(isMat4Equal(originalView, newView));

        // Movement should update view matrix
        originalView = newView;
        camera.moveForward(1.0f, 1.0f);
        newView = camera.getViewMatrix();
        REQUIRE_FALSE(isMat4Equal(originalView, newView));

        // Rotation should update view matrix
        originalView = newView;
        camera.turnLeft(45.0f, 1.0f);
        newView = camera.getViewMatrix();
        REQUIRE_FALSE(isMat4Equal(originalView, newView));
    }

    SECTION("Projection matrix updates") {
        glm::mat4 originalProj = camera.getProjectionMatrix();

        // Change FOV - should update projection matrix
        camera.setFov(60.0f);
        glm::mat4 newProj = camera.getProjectionMatrix();
        REQUIRE_FALSE(isMat4Equal(originalProj, newProj));

        // Change aspect ratio - should update projection matrix
        originalProj = newProj;
        camera.setAspectRatio(16.0f / 9.0f);
        newProj = camera.getProjectionMatrix();
        REQUIRE_FALSE(isMat4Equal(originalProj, newProj));

        // Change near plane - should update projection matrix
        originalProj = newProj;
        camera.setNearPlane(0.5f); // Use a different value from default 0.1f
        newProj = camera.getProjectionMatrix();
        REQUIRE_FALSE(isMat4Equal(originalProj, newProj));

        // Change far plane - should update projection matrix
        originalProj = newProj;
        camera.setFarPlane(500.0f); // Use a different value from default 100.0f
        newProj = camera.getProjectionMatrix();
        REQUIRE_FALSE(isMat4Equal(originalProj, newProj));
    }

    SECTION("Matrix consistency with GLM") {
        glm::vec3 position(1.0f, 2.0f, 3.0f);
        glm::vec3 target(4.0f, 5.0f, 6.0f);
        glm::vec3 up(0.0f, 1.0f, 0.0f);

        camera.setPosition(position);
        camera.setDirection(glm::normalize(target - position));
        camera.setUp(up);

        // Compare with GLM lookAt
        glm::mat4 expectedView = glm::lookAt(position, target, up);

        // Note: Due to internal vector updates, exact match might not be possible
        // But the matrices should be functionally equivalent
        REQUIRE_FALSE(isMat4Equal(camera.getViewMatrix(), glm::mat4(0.0f)));
    }
}

TEST_CASE("Camera edge cases and robustness", "[scene][camera][edge_cases]") {
    SECTION("Zero vectors handling") {
        Camera camera;

        // Setting zero direction should not crash (will be normalized)
        REQUIRE_NOTHROW(camera.setDirection(glm::vec3(0.0f, 0.0f, 0.0f)));

        // Setting zero up vector should not crash
        REQUIRE_NOTHROW(camera.setUp(glm::vec3(0.0f, 0.0f, 0.0f)));

        // Camera should still function
        REQUIRE_NOTHROW(camera.moveForward(1.0f, 1.0f));
    }

    SECTION("Extreme values") {
        Camera camera;

        // Very large position values
        camera.setPosition(glm::vec3(1000000.0f, -1000000.0f, 500000.0f));
        REQUIRE_NOTHROW(camera.moveForward(1.0f, 1.0f));

        // Very small movement values
        REQUIRE_NOTHROW(camera.moveForward(0.001f, 0.001f));

        // Very large movement values
        REQUIRE_NOTHROW(camera.moveForward(1000.0f, 1.0f));

        // Extreme FOV values
        camera.setFov(1.0f);   // Very narrow
        REQUIRE_FALSE(isMat4Equal(camera.getProjectionMatrix(), glm::mat4(0.0f)));

        camera.setFov(179.0f); // Very wide
        REQUIRE_FALSE(isMat4Equal(camera.getProjectionMatrix(), glm::mat4(0.0f)));

        // Extreme aspect ratios
        camera.setAspectRatio(0.1f);  // Very tall
        REQUIRE_FALSE(isMat4Equal(camera.getProjectionMatrix(), glm::mat4(0.0f)));

        camera.setAspectRatio(10.0f); // Very wide
        REQUIRE_FALSE(isMat4Equal(camera.getProjectionMatrix(), glm::mat4(0.0f)));
    }

    SECTION("Zero delta time") {
        Camera camera;
        glm::vec3 originalPos = camera.getPosition();

        // Zero delta time should result in no movement
        camera.moveForward(10.0f, 0.0f);
        REQUIRE(isVec3Equal(camera.getPosition(), originalPos));

        camera.turnLeft(90.0f, 0.0f);
        // Position should still be the same, and camera should function normally
        REQUIRE(isVec3Equal(camera.getPosition(), originalPos));
        REQUIRE_NOTHROW(camera.moveForward(1.0f, 1.0f));
    }

    SECTION("Negative values") {
        Camera camera;

        // Negative speeds (should work like opposite direction)
        glm::vec3 originalPos = camera.getPosition();
        camera.moveForward(-1.0f, 1.0f);
        glm::vec3 backwardPos = camera.getPosition();

        camera.setPosition(originalPos);
        camera.moveBackward(1.0f, 1.0f);
        glm::vec3 explicitBackwardPos = camera.getPosition();

        REQUIRE(isVec3Equal(backwardPos, explicitBackwardPos));
    }
}

TEST_CASE("Camera real-world scenarios", "[scene][camera][real_world]") {
    SECTION("First-person shooter camera") {
        Camera camera;

        // Simulate FPS camera movement
        float deltaTime = 1.0f / 60.0f; // 60 FPS
        float moveSpeed = 5.0f;
        float turnSpeed = 90.0f; // degrees per second

        // Move forward while turning
        for (int frame = 0; frame < 60; ++frame) {
            camera.moveForward(moveSpeed, deltaTime);
            camera.turnLeft(turnSpeed, deltaTime);
        }

        // Should have moved and turned
        REQUIRE_FALSE(isVec3Equal(camera.getPosition(), glm::vec3(0.0f, 0.0f, 1.0f)));

        // Camera should still be functional
        REQUIRE_NOTHROW(camera.moveRight(1.0f, deltaTime));
        REQUIRE_NOTHROW(camera.turnUp(45.0f, deltaTime));
    }

    SECTION("Third-person camera orbit") {
        Camera camera;
        glm::vec3 target(0.0f, 0.0f, 0.0f);
        float radius = 10.0f;

        // Position camera at distance from target
        camera.setPosition(glm::vec3(0.0f, 0.0f, radius));
        camera.setDirection(glm::normalize(target - camera.getPosition()));

        // Orbit around target
        float deltaTime = 1.0f / 60.0f;
        float orbitSpeed = 45.0f; // degrees per second

        for (int frame = 0; frame < 120; ++frame) { // 2 seconds of orbit
            camera.turnLeft(orbitSpeed, deltaTime);

            // Maintain distance from target (simplified orbit)
            glm::vec3 toTarget = target - camera.getPosition();
            float currentDistance = glm::length(toTarget);

            // Distance should remain roughly constant
            REQUIRE(isFloatEqual(currentDistance, radius, 1.0f));
        }
    }

    SECTION("Cinematic camera movement") {
        Camera camera;

        // Smooth camera movement between keyframes
        glm::vec3 startPos(0.0f, 0.0f, 10.0f);
        glm::vec3 endPos(10.0f, 5.0f, 0.0f);

        camera.setPosition(startPos);

        float duration = 2.0f; // 2 seconds
        float deltaTime = 1.0f / 60.0f;
        int totalFrames = static_cast<int>(duration / deltaTime);

        for (int frame = 0; frame <= totalFrames; ++frame) {
            float t = static_cast<float>(frame) / totalFrames;
            glm::vec3 interpolatedPos = glm::mix(startPos, endPos, t);
            camera.setPosition(interpolatedPos);

            // Camera should maintain valid matrices throughout
            REQUIRE_FALSE(isMat4Equal(camera.getViewMatrix(), glm::mat4(0.0f)));
            REQUIRE_FALSE(isMat4Equal(camera.getProjectionMatrix(), glm::mat4(0.0f)));
        }

        // Should end up at target position (last frame sets t=1.0)
        REQUIRE(isVec3Equal(camera.getPosition(), endPos));
    }

    SECTION("Multi-viewport rendering") {
        // Test multiple cameras with different settings
        std::vector<Camera> cameras;

        // Main viewport
        cameras.emplace_back(glm::vec3(0.0f, 0.0f, 5.0f), glm::vec3(0.0f, 0.0f, -1.0f),
                           glm::vec3(0.0f, 1.0f, 0.0f), 60.0f, 16.0f/9.0f, 0.1f, 100.0f);

        // Minimap viewport (top-down)
        cameras.emplace_back(glm::vec3(0.0f, 20.0f, 0.0f), glm::vec3(0.0f, -1.0f, 0.0f),
                           glm::vec3(0.0f, 0.0f, -1.0f), 45.0f, 1.0f, 1.0f, 50.0f);

        // Rear-view mirror
        cameras.emplace_back(glm::vec3(0.0f, 0.0f, 5.0f), glm::vec3(0.0f, 0.0f, 1.0f),
                           glm::vec3(0.0f, 1.0f, 0.0f), 90.0f, 4.0f/3.0f, 0.1f, 20.0f);

        // All cameras should have valid matrices
        for (const auto& cam : cameras) {
            REQUIRE_FALSE(isMat4Equal(cam.getViewMatrix(), glm::mat4(0.0f)));
            REQUIRE_FALSE(isMat4Equal(cam.getProjectionMatrix(), glm::mat4(0.0f)));
        }

        // Cameras should be independent
        cameras[0].moveForward(5.0f, 1.0f);
        REQUIRE_FALSE(isVec3Equal(cameras[0].getPosition(), cameras[1].getPosition()));
    }
}

TEST_CASE("Camera performance characteristics", "[scene][camera][performance]") {
    SECTION("Rapid position updates") {
        Camera camera;

        // Test many position updates
        for (int i = 0; i < 10000; ++i) {
            glm::vec3 pos(static_cast<float>(i), static_cast<float>(i * 2), static_cast<float>(i * 3));
            camera.setPosition(pos);

            // Should maintain valid position
            REQUIRE(isVec3Equal(camera.getPosition(), pos));
        }
    }

    SECTION("Rapid movement operations") {
        Camera camera;
        float deltaTime = 1.0f / 60.0f;

        // Simulate high-frequency updates
        for (int i = 0; i < 1000; ++i) {
            camera.moveForward(1.0f, deltaTime);
            camera.moveRight(0.5f, deltaTime);
            camera.turnLeft(1.0f, deltaTime);
            camera.turnUp(0.5f, deltaTime);
        }

        // Camera should still be functional
        REQUIRE_NOTHROW(camera.moveForward(1.0f, deltaTime));
        REQUIRE_FALSE(isMat4Equal(camera.getViewMatrix(), glm::mat4(0.0f)));
    }

    SECTION("Matrix access performance") {
        Camera camera;

        // Test rapid matrix access
        for (int i = 0; i < 10000; ++i) {
            const auto& viewMatrix = camera.getViewMatrix();
            const auto& projMatrix = camera.getProjectionMatrix();

            // Matrices should be valid
            REQUIRE_FALSE(isMat4Equal(viewMatrix, glm::mat4(0.0f)));
            REQUIRE_FALSE(isMat4Equal(projMatrix, glm::mat4(0.0f)));
        }
    }

    SECTION("Parameter update performance") {
        Camera camera;

        // Test rapid parameter updates
        for (int i = 0; i < 1000; ++i) {
            camera.setFov(45.0f + static_cast<float>(i % 45));
            camera.setAspectRatio(1.0f + static_cast<float>(i % 3));
            camera.setNearPlane(0.1f + static_cast<float>(i % 10) * 0.01f);
            camera.setFarPlane(100.0f + static_cast<float>(i % 100));
        }

        // Should maintain valid projection matrix
        REQUIRE_FALSE(isMat4Equal(camera.getProjectionMatrix(), glm::mat4(0.0f)));
    }
}

TEST_CASE("Camera mathematical properties", "[scene][camera][mathematics]") {
    SECTION("Vector orthogonality") {
        Camera camera;

        // Set known direction and up vectors
        camera.setDirection(glm::vec3(1.0f, 0.0f, 0.0f));
        camera.setUp(glm::vec3(0.0f, 1.0f, 0.0f));

        // Test movement to verify internal vectors are orthogonal
        glm::vec3 originalPos = camera.getPosition();

        // Move forward and right, then check the resulting position
        camera.moveForward(1.0f, 1.0f);
        glm::vec3 forwardPos = camera.getPosition();

        camera.setPosition(originalPos);
        camera.moveRight(1.0f, 1.0f);
        glm::vec3 rightPos = camera.getPosition();

        glm::vec3 forwardDir = forwardPos - originalPos;
        glm::vec3 rightDir = rightPos - originalPos;

        // Forward and right directions should be orthogonal
        REQUIRE(areVec3Orthogonal(forwardDir, rightDir));
    }

    SECTION("Vector normalization") {
        Camera camera;

        // Set unnormalized direction
        camera.setDirection(glm::vec3(3.0f, 4.0f, 5.0f));

        // Movement should still work correctly (direction gets normalized internally)
        glm::vec3 originalPos = camera.getPosition();
        camera.moveForward(1.0f, 1.0f);
        glm::vec3 newPos = camera.getPosition();

        glm::vec3 movement = newPos - originalPos;
        REQUIRE(isFloatEqual(glm::length(movement), 1.0f));
    }

    SECTION("Rotation consistency") {
        Camera camera;

        // Turn left then right should return to original orientation
        glm::vec3 originalPos = camera.getPosition();

        camera.turnLeft(45.0f, 1.0f);
        camera.turnRight(45.0f, 1.0f);

        // Test movement to see if we're back to original direction
        camera.moveForward(1.0f, 1.0f);
        glm::vec3 newPos = camera.getPosition();
        glm::vec3 movement = newPos - originalPos;

        // Should be moving primarily in -Z direction (original forward)
        REQUIRE(movement.z < 0.0f);
        REQUIRE(std::abs(movement.x) < 0.1f); // Minimal X movement
    }

    SECTION("View matrix properties") {
        Camera camera;

        glm::vec3 position(1.0f, 2.0f, 3.0f);
        camera.setPosition(position);

        const auto& viewMatrix = camera.getViewMatrix();

        // View matrix should transform world origin to camera space
        glm::vec4 worldOrigin(0.0f, 0.0f, 0.0f, 1.0f);
        glm::vec4 cameraSpace = viewMatrix * worldOrigin;

        // The result should be related to the negative camera position
        REQUIRE_FALSE(isVec3Equal(glm::vec3(cameraSpace), glm::vec3(0.0f)));
    }
}
