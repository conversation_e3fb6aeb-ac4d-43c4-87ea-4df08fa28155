#define GLM_ENABLE_EXPERIMENTAL

#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp>

#include "scene/scene_graph/scene_graph.hpp"
#include "scene/scene_graph/scene_entities.hpp"
#include "utils/string_id.hpp"
#include "test_utils.hpp"

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/quaternion.hpp>
#include <vector>
#include <string>

using namespace IronFrost;
using Catch::Matchers::WithinAbs;

TEST_CASE("SceneList construction and basic properties", "[scene][scene_graph][construction]") {
    SECTION("Default constructor creates empty scene graph") {
        SceneList sceneGraph;
        
        // Should be able to traverse empty graph without issues
        int callCount = 0;
        sceneGraph.traverse([&](SceneNode&, const glm::mat4&) {
            callCount++;
        });
        
        REQUIRE(callCount == 0);
    }
    
    SECTION("ISceneGraph interface compliance") {
        std::unique_ptr<ISceneGraph> sceneGraph = std::make_unique<SceneList>();
        
        // Should be able to use through interface
        REQUIRE(sceneGraph != nullptr);
        
        // Test basic interface operations
        SceneNode testNode;
        testNode.setPosition(glm::vec3(1.0f, 2.0f, 3.0f));
        
        StringID testName("test_node");
        sceneGraph->insert(testName, std::move(testNode));
        
        SceneNode& retrievedNode = (*sceneGraph)[testName];
        REQUIRE(isVec3Equal(retrievedNode.getPosition(), glm::vec3(1.0f, 2.0f, 3.0f)));
    }
}

TEST_CASE("SceneList node insertion and access", "[scene][scene_graph][insertion]") {
    SceneList sceneGraph;
    
    SECTION("Insert single node") {
        SceneNode node;
        node.setPosition(glm::vec3(5.0f, 10.0f, 15.0f));
        
        StringID nodeName("single_node");
        sceneGraph.insert(nodeName, std::move(node));
        
        SceneNode& retrievedNode = sceneGraph[nodeName];
        REQUIRE(isVec3Equal(retrievedNode.getPosition(), glm::vec3(5.0f, 10.0f, 15.0f)));
    }
    
    SECTION("Insert multiple nodes with different names") {
        SceneNode node1, node2, node3;
        node1.setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        node2.setPosition(glm::vec3(0.0f, 1.0f, 0.0f));
        node3.setPosition(glm::vec3(0.0f, 0.0f, 1.0f));
        
        StringID name1("node_x");
        StringID name2("node_y");
        StringID name3("node_z");
        
        sceneGraph.insert(name1, std::move(node1));
        sceneGraph.insert(name2, std::move(node2));
        sceneGraph.insert(name3, std::move(node3));
        
        REQUIRE(isVec3Equal(sceneGraph[name1].getPosition(), glm::vec3(1.0f, 0.0f, 0.0f)));
        REQUIRE(isVec3Equal(sceneGraph[name2].getPosition(), glm::vec3(0.0f, 1.0f, 0.0f)));
        REQUIRE(isVec3Equal(sceneGraph[name3].getPosition(), glm::vec3(0.0f, 0.0f, 1.0f)));
    }
    
    SECTION("Insert node with scene entities") {
        SceneNode node;
        node.setPosition(glm::vec3(2.0f, 3.0f, 4.0f));
        
        // Add scene entities
        SceneObject obj;
        obj.renderableObjectID = 42;
        obj.uvTiling = glm::vec2(2.0f, 3.0f);
        node.sceneObject = obj;
        
        SceneLight light;
        light.color = glm::vec3(1.0f, 0.5f, 0.2f);
        light.intensity = 2.5f;
        node.sceneLight = light;
        
        StringID nodeName("entity_node");
        sceneGraph.insert(nodeName, std::move(node));
        
        SceneNode& retrievedNode = sceneGraph[nodeName];
        REQUIRE(isVec3Equal(retrievedNode.getPosition(), glm::vec3(2.0f, 3.0f, 4.0f)));
        REQUIRE(retrievedNode.sceneObject.has_value());
        REQUIRE(retrievedNode.sceneObject->renderableObjectID == 42);
        REQUIRE(retrievedNode.sceneLight.has_value());
        REQUIRE(isVec3Equal(retrievedNode.sceneLight->color, glm::vec3(1.0f, 0.5f, 0.2f)));
    }
    
    SECTION("Insert node with children") {
        SceneNode parentNode;
        parentNode.setPosition(glm::vec3(10.0f, 0.0f, 0.0f));
        
        // Add children
        parentNode.children.emplace_back();
        parentNode.children.emplace_back();
        parentNode.children[0].setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        parentNode.children[1].setPosition(glm::vec3(0.0f, 1.0f, 0.0f));
        
        StringID parentName("parent_node");
        sceneGraph.insert(parentName, std::move(parentNode));
        
        SceneNode& retrievedParent = sceneGraph[parentName];
        REQUIRE(isVec3Equal(retrievedParent.getPosition(), glm::vec3(10.0f, 0.0f, 0.0f)));
        REQUIRE(retrievedParent.children.size() == 2);
        REQUIRE(isVec3Equal(retrievedParent.children[0].getPosition(), glm::vec3(1.0f, 0.0f, 0.0f)));
        REQUIRE(isVec3Equal(retrievedParent.children[1].getPosition(), glm::vec3(0.0f, 1.0f, 0.0f)));
    }
    
    SECTION("Overwrite existing node") {
        SceneNode originalNode;
        originalNode.setPosition(glm::vec3(1.0f, 1.0f, 1.0f));
        
        SceneNode newNode;
        newNode.setPosition(glm::vec3(2.0f, 2.0f, 2.0f));
        
        StringID nodeName("overwrite_test");
        
        // Insert original
        sceneGraph.insert(nodeName, std::move(originalNode));
        REQUIRE(isVec3Equal(sceneGraph[nodeName].getPosition(), glm::vec3(1.0f, 1.0f, 1.0f)));

        // Overwrite with new node
        sceneGraph.insert(nodeName, std::move(newNode));
        REQUIRE(isVec3Equal(sceneGraph[nodeName].getPosition(), glm::vec3(2.0f, 2.0f, 2.0f)));
    }
}

TEST_CASE("SceneList node access and modification", "[scene][scene_graph][access]") {
    SceneList sceneGraph;
    
    SECTION("Access and modify existing node") {
        SceneNode node;
        node.setPosition(glm::vec3(1.0f, 2.0f, 3.0f));
        
        StringID nodeName("modify_test");
        sceneGraph.insert(nodeName, std::move(node));

        // Modify through reference
        SceneNode& nodeRef = sceneGraph[nodeName];
        nodeRef.setPosition(glm::vec3(10.0f, 20.0f, 30.0f));
        nodeRef.setScale(glm::vec3(2.0f, 2.0f, 2.0f));
        
        // Verify changes persist
        SceneNode& verifyRef = sceneGraph[nodeName];
        REQUIRE(isVec3Equal(verifyRef.getPosition(), glm::vec3(10.0f, 20.0f, 30.0f)));
        REQUIRE(isVec3Equal(verifyRef.getScale(), glm::vec3(2.0f, 2.0f, 2.0f)));
    }
    
    SECTION("Access non-existent node creates default node") {
        StringID nonExistentName("does_not_exist");
        
        // Accessing non-existent node should create default node
        SceneNode& newNode = sceneGraph[nonExistentName];
        
        // Should have default values
        REQUIRE(isVec3Equal(newNode.getPosition(), glm::vec3(0.0f)));
        REQUIRE(isVec3Equal(newNode.getScale(), glm::vec3(1.0f)));
        REQUIRE(newNode.visible == true);
        REQUIRE(newNode.children.empty());
    }
    
    SECTION("Modify node children through reference") {
        SceneNode parentNode;
        StringID parentName("parent_modify");
        sceneGraph.insert(parentName, std::move(parentNode));

        // Add children through reference
        SceneNode& parentRef = sceneGraph[parentName];
        parentRef.children.emplace_back();
        parentRef.children.emplace_back();
        parentRef.children[0].setPosition(glm::vec3(5.0f, 0.0f, 0.0f));
        parentRef.children[1].setPosition(glm::vec3(0.0f, 5.0f, 0.0f));
        
        // Verify children were added
        SceneNode& verifyRef = sceneGraph[parentName];
        REQUIRE(verifyRef.children.size() == 2);
        REQUIRE(isVec3Equal(verifyRef.children[0].getPosition(), glm::vec3(5.0f, 0.0f, 0.0f)));
        REQUIRE(isVec3Equal(verifyRef.children[1].getPosition(), glm::vec3(0.0f, 5.0f, 0.0f)));
    }
}

TEST_CASE("SceneList traversal functionality", "[scene][scene_graph][traversal]") {
    SceneList sceneGraph;
    
    SECTION("Traverse empty scene graph") {
        int callCount = 0;
        
        sceneGraph.traverse([&](SceneNode&, const glm::mat4&) {
            callCount++;
        });
        
        REQUIRE(callCount == 0);
    }
    
    SECTION("Traverse single node") {
        SceneNode node;
        node.setPosition(glm::vec3(3.0f, 4.0f, 5.0f));
        
        StringID nodeName("single_traverse");
        sceneGraph.insert(nodeName, std::move(node));

        int callCount = 0;
        glm::vec3 receivedPosition;
        glm::mat4 receivedTransform;
        
        sceneGraph.traverse([&](SceneNode& n, const glm::mat4& transform) {
            callCount++;
            receivedPosition = n.getPosition();
            receivedTransform = transform;
        });
        
        REQUIRE(callCount == 1);
        REQUIRE(isVec3Equal(receivedPosition, glm::vec3(3.0f, 4.0f, 5.0f)));
        
        // Transform should be the node's local transform (since no parent)
        glm::mat4 expectedTransform = glm::translate(glm::mat4(1.0f), glm::vec3(3.0f, 4.0f, 5.0f));
        REQUIRE(isMat4Equal(receivedTransform, expectedTransform));
    }

    SECTION("Traverse multiple nodes") {
        SceneNode node1, node2, node3;
        node1.setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        node2.setPosition(glm::vec3(0.0f, 1.0f, 0.0f));
        node3.setPosition(glm::vec3(0.0f, 0.0f, 1.0f));

        StringID name1("node1");
        StringID name2("node2");
        StringID name3("node3");

        sceneGraph.insert(name1, std::move(node1));
        sceneGraph.insert(name2, std::move(node2));
        sceneGraph.insert(name3, std::move(node3));

        std::vector<glm::vec3> receivedPositions;
        std::vector<glm::mat4> receivedTransforms;

        sceneGraph.traverse([&](SceneNode& node, const glm::mat4& transform) {
            receivedPositions.push_back(node.getPosition());
            receivedTransforms.push_back(transform);
        });

        REQUIRE(receivedPositions.size() == 3);
        REQUIRE(receivedTransforms.size() == 3);

        // Note: Order is not guaranteed in unordered_map, so we check that all positions are present
        bool found1 = false, found2 = false, found3 = false;
        for (const auto& pos : receivedPositions) {
            if (isVec3Equal(pos, glm::vec3(1.0f, 0.0f, 0.0f))) found1 = true;
            if (isVec3Equal(pos, glm::vec3(0.0f, 1.0f, 0.0f))) found2 = true;
            if (isVec3Equal(pos, glm::vec3(0.0f, 0.0f, 1.0f))) found3 = true;
        }

        REQUIRE(found1);
        REQUIRE(found2);
        REQUIRE(found3);
    }

    SECTION("Traverse nodes with children") {
        SceneNode parentNode;
        parentNode.setPosition(glm::vec3(10.0f, 0.0f, 0.0f));

        // Add children to parent
        parentNode.children.emplace_back();
        parentNode.children.emplace_back();
        parentNode.children[0].setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        parentNode.children[1].setPosition(glm::vec3(0.0f, 1.0f, 0.0f));

        // Add grandchild
        parentNode.children[0].children.emplace_back();
        parentNode.children[0].children[0].setPosition(glm::vec3(0.0f, 0.0f, 1.0f));

        StringID parentName("parent_with_children");
        sceneGraph.insert(parentName, std::move(parentNode));

        std::vector<glm::vec3> receivedPositions;
        int callCount = 0;

        sceneGraph.traverse([&](SceneNode& node, const glm::mat4& transform) {
            callCount++;
            receivedPositions.push_back(node.getPosition());
        });

        // Should visit parent + 2 children + 1 grandchild = 4 nodes
        REQUIRE(callCount == 4);
        REQUIRE(receivedPositions.size() == 4);

        // Check that all expected positions are present
        bool foundParent = false, foundChild1 = false, foundChild2 = false, foundGrandchild = false;
        for (const auto& pos : receivedPositions) {
            if (isVec3Equal(pos, glm::vec3(10.0f, 0.0f, 0.0f))) foundParent = true;
            if (isVec3Equal(pos, glm::vec3(1.0f, 0.0f, 0.0f))) foundChild1 = true;
            if (isVec3Equal(pos, glm::vec3(0.0f, 1.0f, 0.0f))) foundChild2 = true;
            if (isVec3Equal(pos, glm::vec3(0.0f, 0.0f, 1.0f))) foundGrandchild = true;
        }

        REQUIRE(foundParent);
        REQUIRE(foundChild1);
        REQUIRE(foundChild2);
        REQUIRE(foundGrandchild);
    }

    SECTION("Traverse with transform verification") {
        SceneNode parentNode;
        parentNode.setPosition(glm::vec3(5.0f, 0.0f, 0.0f));

        parentNode.children.emplace_back();
        parentNode.children[0].setPosition(glm::vec3(2.0f, 0.0f, 0.0f));

        StringID parentName("transform_test");
        sceneGraph.insert(parentName, std::move(parentNode));

        std::vector<glm::mat4> transforms;
        std::vector<bool> isParent;

        sceneGraph.traverse([&](SceneNode& node, const glm::mat4& transform) {
            transforms.push_back(transform);
            // Parent has position (5,0,0), child has (2,0,0)
            isParent.push_back(isVec3Equal(node.getPosition(), glm::vec3(5.0f, 0.0f, 0.0f)));
        });

        REQUIRE(transforms.size() == 2);

        // Find parent and child transforms
        glm::mat4 parentTransform, childTransform;
        for (size_t i = 0; i < transforms.size(); ++i) {
            if (isParent[i]) {
                parentTransform = transforms[i];
            } else {
                childTransform = transforms[i];
            }
        }

        // Parent transform should be translation by (5,0,0)
        glm::mat4 expectedParentTransform = glm::translate(glm::mat4(1.0f), glm::vec3(5.0f, 0.0f, 0.0f));
        REQUIRE(isMat4Equal(parentTransform, expectedParentTransform));

        // Child transform should be parent * child = translate(5,0,0) * translate(2,0,0) = translate(7,0,0)
        glm::mat4 expectedChildTransform = glm::translate(glm::mat4(1.0f), glm::vec3(7.0f, 0.0f, 0.0f));
        REQUIRE(isMat4Equal(childTransform, expectedChildTransform));
    }
}

TEST_CASE("SceneList StringID integration", "[scene][scene_graph][string_id]") {
    SceneList sceneGraph;

    SECTION("Different StringID construction methods") {
        SceneNode node1, node2, node3;
        node1.setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        node2.setPosition(glm::vec3(0.0f, 1.0f, 0.0f));
        node3.setPosition(glm::vec3(0.0f, 0.0f, 1.0f));

        // Different ways to create StringID
        StringID name1("string_literal");
        std::string str = "std_string";
        StringID name2(str);
        StringID name3 = StringID("direct_construction");

        sceneGraph.insert(name1, std::move(node1));
        sceneGraph.insert(name2, std::move(node2));
        sceneGraph.insert(name3, std::move(node3));

        REQUIRE(isVec3Equal(sceneGraph[name1].getPosition(), glm::vec3(1.0f, 0.0f, 0.0f)));
        REQUIRE(isVec3Equal(sceneGraph[name2].getPosition(), glm::vec3(0.0f, 1.0f, 0.0f)));
        REQUIRE(isVec3Equal(sceneGraph[name3].getPosition(), glm::vec3(0.0f, 0.0f, 1.0f)));
    }

    SECTION("StringID equality and uniqueness") {
        SceneNode node;
        node.setPosition(glm::vec3(42.0f, 0.0f, 0.0f));

        StringID name1("unique_name");
        StringID name2("unique_name"); // Same string, should be same ID
        StringID name3("different_name");

        sceneGraph.insert(name1, std::move(node));

        // Should be able to access with equivalent StringID
        REQUIRE(isVec3Equal(sceneGraph[name2].getPosition(), glm::vec3(42.0f, 0.0f, 0.0f)));

        // Different name should create new default node
        SceneNode& differentNode = sceneGraph[name3];
        REQUIRE(isVec3Equal(differentNode.getPosition(), glm::vec3(0.0f))); // Default position
    }
}

TEST_CASE("SceneList complex scene scenarios", "[scene][scene_graph][complex]") {
    SceneList sceneGraph;

    SECTION("Game-like scene structure") {
        // Create a typical game scene structure

        // Root objects
        SceneNode player;
        player.setPosition(glm::vec3(0.0f, 0.0f, 0.0f));
        player.setScale(glm::vec3(1.0f, 1.0f, 1.0f));

        SceneObject playerObj;
        playerObj.renderableObjectID = 1;
        player.sceneObject = playerObj;

        // Player weapon (child of player)
        player.children.emplace_back();
        player.children[0].setPosition(glm::vec3(1.0f, 0.5f, 0.0f)); // Weapon offset

        SceneModel weaponModel;
        weaponModel.renderableModelID = 10;
        player.children[0].sceneModel = weaponModel;

        // Environment objects
        SceneNode environment;
        environment.setPosition(glm::vec3(0.0f, 0.0f, 0.0f));

        // Multiple environment objects
        for (int i = 0; i < 5; ++i) {
            environment.children.emplace_back();
            environment.children[i].setPosition(glm::vec3(static_cast<float>(i * 10), 0.0f, 0.0f));

            SceneObject envObj;
            envObj.renderableObjectID = 100 + i;
            environment.children[i].sceneObject = envObj;
        }

        // Lighting
        SceneNode lightNode;
        lightNode.setPosition(glm::vec3(0.0f, 10.0f, 0.0f));

        SceneLight light;
        light.color = glm::vec3(1.0f, 1.0f, 1.0f);
        light.intensity = 1.0f;
        light.constant = 1.0f;
        light.linear = 0.09f;
        light.quadratic = 0.032f;
        lightNode.sceneLight = light;

        // Insert into scene graph
        StringID playerName("player");
        StringID envName("environment");
        StringID lightName("main_light");

        sceneGraph.insert(playerName, std::move(player));
        sceneGraph.insert(envName, std::move(environment));
        sceneGraph.insert(lightName, std::move(lightNode));

        // Verify structure
        REQUIRE(sceneGraph[playerName].sceneObject.has_value());
        REQUIRE(sceneGraph[playerName].children.size() == 1);
        REQUIRE(sceneGraph[playerName].children[0].sceneModel.has_value());

        REQUIRE(sceneGraph[envName].children.size() == 5);
        for (int i = 0; i < 5; ++i) {
            REQUIRE(sceneGraph[envName].children[i].sceneObject.has_value());
            REQUIRE(sceneGraph[envName].children[i].sceneObject->renderableObjectID == 100 + i);
        }

        REQUIRE(sceneGraph[lightName].sceneLight.has_value());
        REQUIRE(isVec3Equal(sceneGraph[lightName].sceneLight->color, glm::vec3(1.0f, 1.0f, 1.0f)));

        // Test traversal counts all nodes
        int totalNodes = 0;
        int objectNodes = 0;
        int modelNodes = 0;
        int lightNodes = 0;

        sceneGraph.traverse([&](SceneNode& node, const glm::mat4& transform) {
            totalNodes++;
            if (node.sceneObject.has_value()) objectNodes++;
            if (node.sceneModel.has_value()) modelNodes++;
            if (node.sceneLight.has_value()) lightNodes++;
        });

        // 3 root nodes + 1 weapon + 5 environment objects = 9 total
        REQUIRE(totalNodes == 9);
        REQUIRE(objectNodes == 6); // Player + 5 environment objects
        REQUIRE(modelNodes == 1);  // Weapon
        REQUIRE(lightNodes == 1);  // Main light
    }

    SECTION("Dynamic scene modification") {
        // Start with basic scene
        SceneNode rootNode;
        rootNode.setPosition(glm::vec3(0.0f, 0.0f, 0.0f));

        StringID rootName("dynamic_root");
        sceneGraph.insert(rootName, std::move(rootNode));

        // Verify initial state
        int initialCount = 0;
        sceneGraph.traverse([&](SceneNode&, const glm::mat4&) { initialCount++; });
        REQUIRE(initialCount == 1);

        // Add children dynamically
        SceneNode& rootRef = sceneGraph[rootName];
        rootRef.children.emplace_back();
        rootRef.children.emplace_back();
        rootRef.children[0].setPosition(glm::vec3(5.0f, 0.0f, 0.0f));
        rootRef.children[1].setPosition(glm::vec3(-5.0f, 0.0f, 0.0f));

        // Verify children were added
        int afterAddCount = 0;
        sceneGraph.traverse([&](SceneNode&, const glm::mat4&) { afterAddCount++; });
        REQUIRE(afterAddCount == 3); // Root + 2 children

        // Add more nodes to scene graph
        SceneNode newNode;
        newNode.setPosition(glm::vec3(10.0f, 10.0f, 10.0f));
        StringID newName("new_dynamic_node");
        sceneGraph.insert(newName, std::move(newNode));

        // Verify total count
        int finalCount = 0;
        sceneGraph.traverse([&](SceneNode&, const glm::mat4&) { finalCount++; });
        REQUIRE(finalCount == 4); // Previous 3 + 1 new
    }
}

TEST_CASE("SceneList edge cases and robustness", "[scene][scene_graph][edge_cases]") {
    SceneList sceneGraph;

    SECTION("Empty string StringID") {
        SceneNode node;
        node.setPosition(glm::vec3(1.0f, 2.0f, 3.0f));

        StringID emptyName("");
        sceneGraph.insert(emptyName, std::move(node));

        SceneNode& retrievedNode = sceneGraph[emptyName];
        REQUIRE(isVec3Equal(retrievedNode.getPosition(), glm::vec3(1.0f, 2.0f, 3.0f)));
    }

    SECTION("Very long StringID names") {
        SceneNode node;
        node.setPosition(glm::vec3(5.0f, 5.0f, 5.0f));

        std::string longName(1000, 'a'); // 1000 character name
        StringID longStringID(longName);

        sceneGraph.insert(longStringID, std::move(node));

        SceneNode& retrievedNode = sceneGraph[longStringID];
        REQUIRE(isVec3Equal(retrievedNode.getPosition(), glm::vec3(5.0f, 5.0f, 5.0f)));
    }

    SECTION("Special characters in StringID") {
        SceneNode node;
        node.setPosition(glm::vec3(7.0f, 8.0f, 9.0f));

        StringID specialName("node@#$%^&*()_+-=[]{}|;':\",./<>?");
        sceneGraph.insert(specialName, std::move(node));

        SceneNode& retrievedNode = sceneGraph[specialName];
        REQUIRE(isVec3Equal(retrievedNode.getPosition(), glm::vec3(7.0f, 8.0f, 9.0f)));
    }

    SECTION("Large number of nodes performance") {
        const int nodeCount = 1000;

        // Insert many nodes
        for (int i = 0; i < nodeCount; ++i) {
            SceneNode node;
            node.setPosition(glm::vec3(static_cast<float>(i), 0.0f, 0.0f));

            StringID nodeName("node_" + std::to_string(i));
            sceneGraph.insert(nodeName, std::move(node));
        }

        // Verify all nodes can be accessed
        for (int i = 0; i < nodeCount; ++i) {
            StringID nodeName("node_" + std::to_string(i));
            SceneNode& node = sceneGraph[nodeName];
            REQUIRE(isVec3Equal(node.getPosition(), glm::vec3(static_cast<float>(i), 0.0f, 0.0f)));
        }

        // Verify traversal visits all nodes
        int traversalCount = 0;
        sceneGraph.traverse([&](SceneNode&, const glm::mat4&) {
            traversalCount++;
        });

        REQUIRE(traversalCount == nodeCount);
    }

    SECTION("Deep hierarchy performance") {
        SceneNode rootNode;
        StringID rootName("deep_root");
        sceneGraph.insert(rootName, std::move(rootNode));

        // Create deep hierarchy (100 levels)
        SceneNode* currentNode = &sceneGraph[rootName];
        for (int i = 0; i < 100; ++i) {
            currentNode->children.emplace_back();
            currentNode->children[0].setPosition(glm::vec3(static_cast<float>(i), 0.0f, 0.0f));
            currentNode = &currentNode->children[0];
        }

        // Verify traversal handles deep hierarchy
        int depthCount = 0;
        sceneGraph.traverse([&](SceneNode&, const glm::mat4&) {
            depthCount++;
        });

        REQUIRE(depthCount == 101); // Root + 100 descendants
    }

    SECTION("Empty callback traversal") {
        SceneNode node;
        StringID nodeName("callback_test");
        sceneGraph.insert(nodeName, std::move(node));

        // Should not crash with empty callback
        sceneGraph.traverse([](SceneNode&, const glm::mat4&) {
            // Empty callback
        });

        SUCCEED("Empty callback traversal completed without crash");
    }
}
