#include <catch2/catch_test_macros.hpp>

#include "assets/asset_data_types.hpp"
#include "test_utils.hpp"

#include <glm/glm.hpp>
#include <glm/gtc/constants.hpp>
#include <cmath>

using namespace IronFrost;

// Helper function to create a vertex with position, UV, and normal
Vertex createVertex(const glm::vec3& pos, const glm::vec2& uv, const glm::vec3& normal) {
    Vertex vertex;
    vertex.data[0] = pos.x; vertex.data[1] = pos.y; vertex.data[2] = pos.z;
    vertex.data[3] = uv.x; vertex.data[4] = uv.y;
    vertex.data[5] = normal.x; vertex.data[6] = normal.y; vertex.data[7] = normal.z;
    // Initialize tangent and bitangent to zero
    for (int i = 8; i < 14; ++i) {
        vertex.data[i] = 0.0f;
    }
    return vertex;
}

TEST_CASE("Vertex basic functionality", "[assets][vertex][basic]") {
    SECTION("Vertex position getter/setter") {
        Vertex vertex;
        vertex.data[0] = 1.0f;
        vertex.data[1] = 2.0f;
        vertex.data[2] = 3.0f;
        
        glm::vec3 pos = vertex.getPosition();
        REQUIRE(pos.x == 1.0f);
        REQUIRE(pos.y == 2.0f);
        REQUIRE(pos.z == 3.0f);
    }
    
    SECTION("Vertex UV getter") {
        Vertex vertex;
        vertex.data[3] = 0.5f;
        vertex.data[4] = 0.7f;
        
        glm::vec2 uv = vertex.getUV();
        REQUIRE(uv.x == 0.5f);
        REQUIRE(uv.y == 0.7f);
    }
    
    SECTION("Vertex normal getter") {
        Vertex vertex;
        vertex.data[5] = 0.0f;
        vertex.data[6] = 1.0f;
        vertex.data[7] = 0.0f;
        
        glm::vec3 normal = vertex.getNormal();
        REQUIRE(normal.x == 0.0f);
        REQUIRE(normal.y == 1.0f);
        REQUIRE(normal.z == 0.0f);
    }
    
    SECTION("Vertex tangent setter/getter") {
        Vertex vertex;
        glm::vec3 tangent(1.0f, 0.0f, 0.0f);
        
        vertex.setTangent(tangent);
        glm::vec3 retrievedTangent = vertex.getTangent();
        
        REQUIRE(retrievedTangent.x == 1.0f);
        REQUIRE(retrievedTangent.y == 0.0f);
        REQUIRE(retrievedTangent.z == 0.0f);
    }
    
    SECTION("Vertex bitangent setter/getter") {
        Vertex vertex;
        glm::vec3 bitangent(0.0f, 0.0f, 1.0f);
        
        vertex.setBitangent(bitangent);
        glm::vec3 retrievedBitangent = vertex.getBitangent();
        
        REQUIRE(retrievedBitangent.x == 0.0f);
        REQUIRE(retrievedBitangent.y == 0.0f);
        REQUIRE(retrievedBitangent.z == 1.0f);
    }
}

TEST_CASE("MeshData calculateTangentsAndBitangents", "[assets][mesh_data][tangents]") {
    SECTION("Simple triangle with proper UV mapping") {
        MeshData meshData;
        
        // Create a simple triangle in XY plane with proper UV coordinates
        meshData.vertices = {
            createVertex(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec2(0.0f, 0.0f), glm::vec3(0.0f, 0.0f, 1.0f)),
            createVertex(glm::vec3(1.0f, 0.0f, 0.0f), glm::vec2(1.0f, 0.0f), glm::vec3(0.0f, 0.0f, 1.0f)),
            createVertex(glm::vec3(0.0f, 1.0f, 0.0f), glm::vec2(0.0f, 1.0f), glm::vec3(0.0f, 0.0f, 1.0f))
        };
        meshData.indices = {0, 1, 2};
        
        meshData.calculateTangentsAndBitangents();
        
        // For this triangle, tangent should point in +X direction, bitangent in +Y direction
        for (const auto& vertex : meshData.vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Verify tangents and bitangents are normalized
            REQUIRE(std::abs(glm::length(tangent) - 1.0f) < 0.001f);
            REQUIRE(std::abs(glm::length(bitangent) - 1.0f) < 0.001f);
            
            // For this specific triangle, tangent should be approximately (1,0,0)
            REQUIRE(isApproxEqual(tangent, glm::vec3(1.0f, 0.0f, 0.0f)));
            // Bitangent should be approximately (0,1,0)
            REQUIRE(isApproxEqual(bitangent, glm::vec3(0.0f, 1.0f, 0.0f)));
        }
    }
    
    SECTION("Quad with two triangles") {
        MeshData meshData;
        
        // Create a quad made of two triangles
        meshData.vertices = {
            createVertex(glm::vec3(-1.0f, -1.0f, 0.0f), glm::vec2(0.0f, 0.0f), glm::vec3(0.0f, 0.0f, 1.0f)),
            createVertex(glm::vec3( 1.0f, -1.0f, 0.0f), glm::vec2(1.0f, 0.0f), glm::vec3(0.0f, 0.0f, 1.0f)),
            createVertex(glm::vec3( 1.0f,  1.0f, 0.0f), glm::vec2(1.0f, 1.0f), glm::vec3(0.0f, 0.0f, 1.0f)),
            createVertex(glm::vec3(-1.0f,  1.0f, 0.0f), glm::vec2(0.0f, 1.0f), glm::vec3(0.0f, 0.0f, 1.0f))
        };
        meshData.indices = {0, 1, 2, 0, 2, 3}; // Two triangles
        
        meshData.calculateTangentsAndBitangents();
        
        // All vertices should have consistent tangent space
        for (const auto& vertex : meshData.vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Verify tangents and bitangents are normalized
            REQUIRE(std::abs(glm::length(tangent) - 1.0f) < 0.001f);
            REQUIRE(std::abs(glm::length(bitangent) - 1.0f) < 0.001f);
            
            // For this quad, tangent should point in +X direction
            REQUIRE(isApproxEqual(tangent, glm::vec3(1.0f, 0.0f, 0.0f)));
            // Bitangent should point in +Y direction
            REQUIRE(isApproxEqual(bitangent, glm::vec3(0.0f, 1.0f, 0.0f)));
        }
    }
    
    SECTION("Empty mesh data") {
        MeshData meshData;
        
        // Should not crash with empty data
        meshData.calculateTangentsAndBitangents();
        
        REQUIRE(meshData.vertices.empty());
        REQUIRE(meshData.indices.empty());
    }
    
    SECTION("Mesh with degenerate triangle (zero UV area)") {
        MeshData meshData;
        
        // Create a triangle with identical UV coordinates (degenerate case)
        meshData.vertices = {
            createVertex(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec2(0.5f, 0.5f), glm::vec3(0.0f, 0.0f, 1.0f)),
            createVertex(glm::vec3(1.0f, 0.0f, 0.0f), glm::vec2(0.5f, 0.5f), glm::vec3(0.0f, 0.0f, 1.0f)),
            createVertex(glm::vec3(0.0f, 1.0f, 0.0f), glm::vec2(0.5f, 0.5f), glm::vec3(0.0f, 0.0f, 1.0f))
        };
        meshData.indices = {0, 1, 2};
        
        // Should not crash even with degenerate UV coordinates
        meshData.calculateTangentsAndBitangents();
        
        // Tangents and bitangents may be NaN or infinity in this case, but should not crash
        for (const auto& vertex : meshData.vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Just verify we can access the values without crashing
            (void)tangent.x; (void)tangent.y; (void)tangent.z;
            (void)bitangent.x; (void)bitangent.y; (void)bitangent.z;
        }
        
        REQUIRE(true); // Test passes if no crash occurs
    }
    
    SECTION("Single triangle with different orientations") {
        MeshData meshData;
        
        // Create a triangle in XZ plane (different orientation)
        meshData.vertices = {
            createVertex(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec2(0.0f, 0.0f), glm::vec3(0.0f, 1.0f, 0.0f)),
            createVertex(glm::vec3(1.0f, 0.0f, 0.0f), glm::vec2(1.0f, 0.0f), glm::vec3(0.0f, 1.0f, 0.0f)),
            createVertex(glm::vec3(0.0f, 0.0f, 1.0f), glm::vec2(0.0f, 1.0f), glm::vec3(0.0f, 1.0f, 0.0f))
        };
        meshData.indices = {0, 1, 2};
        
        meshData.calculateTangentsAndBitangents();
        
        // Verify tangents and bitangents are calculated and normalized
        for (const auto& vertex : meshData.vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            
            // Should be normalized (or zero in degenerate cases)
            float tangentLength = glm::length(tangent);
            float bitangentLength = glm::length(bitangent);
            
            if (tangentLength > 0.001f) {
                REQUIRE(std::abs(tangentLength - 1.0f) < 0.001f);
            }
            if (bitangentLength > 0.001f) {
                REQUIRE(std::abs(bitangentLength - 1.0f) < 0.001f);
            }
        }
    }
    
    SECTION("Tangent space orthogonality") {
        MeshData meshData;
        
        // Create a simple triangle
        meshData.vertices = {
            createVertex(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec2(0.0f, 0.0f), glm::vec3(0.0f, 0.0f, 1.0f)),
            createVertex(glm::vec3(1.0f, 0.0f, 0.0f), glm::vec2(1.0f, 0.0f), glm::vec3(0.0f, 0.0f, 1.0f)),
            createVertex(glm::vec3(0.0f, 1.0f, 0.0f), glm::vec2(0.0f, 1.0f), glm::vec3(0.0f, 0.0f, 1.0f))
        };
        meshData.indices = {0, 1, 2};
        
        meshData.calculateTangentsAndBitangents();
        
        // Check that tangent, bitangent, and normal form an orthogonal basis
        for (const auto& vertex : meshData.vertices) {
            glm::vec3 tangent = vertex.getTangent();
            glm::vec3 bitangent = vertex.getBitangent();
            glm::vec3 normal = vertex.getNormal();
            
            // Check orthogonality (dot product should be close to 0)
            float tangentBitangentDot = glm::dot(tangent, bitangent);
            float tangentNormalDot = glm::dot(tangent, normal);
            float bitangentNormalDot = glm::dot(bitangent, normal);
            
            REQUIRE(std::abs(tangentBitangentDot) < 0.1f);
            REQUIRE(std::abs(tangentNormalDot) < 0.1f);
            REQUIRE(std::abs(bitangentNormalDot) < 0.1f);
        }
    }
}

TEST_CASE("MeshData basic functionality", "[assets][mesh_data][basic]") {
    SECTION("Empty mesh data initialization") {
        MeshData meshData;
        
        REQUIRE(meshData.vertices.empty());
        REQUIRE(meshData.indices.empty());
    }
    
    SECTION("Mesh data with vertices and indices") {
        MeshData meshData;
        
        meshData.vertices.push_back(createVertex(glm::vec3(0, 0, 0), glm::vec2(0, 0), glm::vec3(0, 0, 1)));
        meshData.vertices.push_back(createVertex(glm::vec3(1, 0, 0), glm::vec2(1, 0), glm::vec3(0, 0, 1)));
        meshData.vertices.push_back(createVertex(glm::vec3(0, 1, 0), glm::vec2(0, 1), glm::vec3(0, 0, 1)));
        
        meshData.indices = {0, 1, 2};
        
        REQUIRE(meshData.vertices.size() == 3);
        REQUIRE(meshData.indices.size() == 3);
        
        // Verify vertex data
        REQUIRE(meshData.vertices[0].getPosition() == glm::vec3(0, 0, 0));
        REQUIRE(meshData.vertices[1].getPosition() == glm::vec3(1, 0, 0));
        REQUIRE(meshData.vertices[2].getPosition() == glm::vec3(0, 1, 0));
    }
}
